# AIKeySpider - API Key 爬取工具设计文档

## 项目概述

AIKeySpider 是一个专门用于从代码仓库、搜索引擎等多种来源搜索和爬取各类AI服务API Key的工具。主要目标是帮助安全研究人员和开发者发现可能泄露的API密钥。

## 目标API Key类型

### 主要目标
- **Gemini API Key** (Google AI)
- **OpenRouter API Key**
- **OpenAI API Key**
- **Anthropic Claude API Key**
- **Cohere API Key**
- **Hugging Face API Token**
- **Replicate API Token**

### 扩展目标
- AWS Access Key
- Azure API Key
- GitHub Token
- Discord Bot Token
- Telegram Bot Token

## 核心功能模块

### 1. 搜索引擎爬虫模块 (SearchEngine)
- **Google搜索**: 使用特定关键词搜索可能包含API Key的页面
- **GitHub搜索**: 通过GitHub API搜索代码仓库中的敏感信息
- **GitLab搜索**: 搜索GitLab公开仓库
- **Pastebin搜索**: 搜索代码粘贴网站

### 2. 代码仓库扫描模块 (RepoScanner)
- **本地仓库扫描**: 扫描指定目录下的代码文件
- **远程仓库克隆扫描**: 克隆远程仓库进行扫描
- **文件类型过滤**: 重点扫描配置文件、环境变量文件等

### 3. 模式匹配模块 (PatternMatcher)
- **正则表达式引擎**: 基于各种API Key的格式特征进行匹配
- **熵值检测**: 检测高熵值字符串
- **上下文分析**: 分析Key周围的上下文信息

### 4. 验证模块 (Validator)
- **API Key有效性验证**: 通过实际API调用验证Key是否有效
- **权限级别检测**: 检测API Key的权限范围
- **速率限制处理**: 避免验证过程中触发API限制

### 5. 结果管理模块 (ResultManager)
- **去重处理**: 避免重复记录相同的API Key
- **分类存储**: 按照API类型和来源分类存储结果
- **导出功能**: 支持多种格式导出(JSON, CSV, HTML)

## 技术架构

### 编程语言
- **Python 3.8+**: 主要开发语言
- **异步编程**: 使用asyncio提高爬取效率

### 核心依赖库
```
requests          # HTTP请求
aiohttp          # 异步HTTP请求
beautifulsoup4   # HTML解析
selenium         # 浏览器自动化
regex            # 正则表达式
gitpython        # Git操作
python-dotenv    # 环境变量管理
click            # 命令行界面
rich             # 美化输出
sqlalchemy       # 数据库ORM
```

### 数据存储
- **SQLite**: 本地结果存储
- **JSON**: 配置文件和临时数据
- **CSV**: 结果导出格式

## 项目结构

```
AIKeySpider/
├── src/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── spider.py          # 主爬虫类
│   │   ├── patterns.py        # API Key模式定义
│   │   └── validator.py       # Key验证器
│   ├── engines/
│   │   ├── __init__.py
│   │   ├── search_engine.py   # 搜索引擎爬虫
│   │   ├── github_scanner.py  # GitHub扫描器
│   │   ├── repo_scanner.py    # 代码仓库扫描器
│   │   └── pastebin_scanner.py # Pastebin扫描器
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── file_utils.py      # 文件操作工具
│   │   ├── network_utils.py   # 网络工具
│   │   └── crypto_utils.py    # 加密相关工具
│   └── models/
│       ├── __init__.py
│       ├── database.py        # 数据库模型
│       └── result.py          # 结果数据模型
├── config/
│   ├── patterns.json          # API Key模式配置
│   ├── search_queries.json    # 搜索查询配置
│   └── settings.json          # 全局设置
├── tests/
│   ├── __init__.py
│   ├── test_patterns.py
│   ├── test_validators.py
│   └── test_scanners.py
├── docs/
│   ├── API.md                 # API文档
│   ├── USAGE.md              # 使用说明
│   └── PATTERNS.md           # 模式说明
├── scripts/
│   ├── setup.py              # 安装脚本
│   └── run_spider.py         # 运行脚本
├── requirements.txt
├── README.md
├── LICENSE
└── .gitignore
```

## API Key模式定义

### Gemini API Key
- **格式**: `AIza[0-9A-Za-z-_]{35}`
- **示例**: `AIzaSyDaGmWKa4JsXZ-HjGw7ISLan_Tos80ksOE`
- **上下文关键词**: gemini, google, ai, api_key

### OpenRouter API Key
- **格式**: `sk-or-[a-zA-Z0-9-_]{43,}`
- **示例**: `sk-or-v1-1234567890abcdef1234567890abcdef12345678`
- **上下文关键词**: openrouter, sk-or

### OpenAI API Key
- **格式**: `sk-[a-zA-Z0-9]{48}`
- **示例**: `sk-1234567890abcdef1234567890abcdef12345678901234`
- **上下文关键词**: openai, gpt, api_key

## 安全考虑

### 1. 合规性
- 仅用于安全研究和漏洞发现
- 遵守各平台的使用条款
- 不进行恶意利用

### 2. 隐私保护
- 敏感信息脱敏处理
- 本地存储加密
- 不上传敏感数据

### 3. 速率限制
- 实现请求间隔控制
- 避免对目标服务造成压力
- 支持代理轮换

## 使用场景

### 1. 安全审计
- 企业内部代码仓库安全检查
- 开源项目安全评估
- 个人项目安全自查

### 2. 威胁情报
- 监控公开泄露的API Key
- 及时发现安全威胁
- 建立威胁情报数据库

### 3. 研究用途
- API Key泄露模式研究
- 安全意识培训材料
- 漏洞发现方法论研究

## 开发计划

### Phase 1: 核心框架 (1-2周)
- [ ] 项目结构搭建
- [ ] 基础模式匹配引擎
- [ ] 简单的文件扫描功能
- [ ] 基础CLI界面

### Phase 2: 搜索引擎集成 (2-3周)
- [ ] GitHub API集成
- [ ] Google搜索集成
- [ ] 结果去重和存储
- [ ] 基础验证功能

### Phase 3: 高级功能 (2-3周)
- [ ] 异步爬取优化
- [ ] 高级模式匹配
- [ ] 完整的验证系统
- [ ] Web界面开发

### Phase 4: 优化和扩展 (1-2周)
- [ ] 性能优化
- [ ] 更多API Key类型支持
- [ ] 高级过滤和分析
- [ ] 文档完善

## 风险评估

### 技术风险
- **反爬虫机制**: 目标网站可能有反爬虫保护
- **API限制**: 各种API服务的调用限制
- **法律风险**: 需要确保合规使用

### 缓解措施
- 实现智能延迟和代理轮换
- 提供配置选项控制爬取行为
- 添加免责声明和使用指南

## 总结

AIKeySpider将是一个功能强大且负责任的API Key发现工具，专注于帮助安全研究人员和开发者识别潜在的安全风险。通过模块化设计和可扩展架构，该工具将能够适应不断变化的API Key格式和新的搜索来源。
