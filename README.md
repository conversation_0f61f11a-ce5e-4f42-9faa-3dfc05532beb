# AIKeySpider

一个专业的API Key爬取工具，支持从代码仓库、搜索引擎等多种来源搜索和发现各类AI服务API密钥。

## 特性

- 🔍 **多源搜索**: 支持GitHub、Google搜索、Pastebin等多种来源
- 🤖 **智能爬取**: API优先，Playwright备选的自动切换机制
- 🛡️ **反检测**: User-Agent轮换、代理轮换、请求间隔控制
- 🎯 **精准匹配**: 基于正则表达式和熵值检测的模式匹配
- ⚡ **高性能**: 异步并发处理，支持批量扫描
- 🔐 **安全合规**: 注重隐私保护和合规使用

## 支持的API Key类型

- **AI服务**: Gemini、OpenRouter、OpenAI、Claude、Cohere、Hugging Face
- **开发平台**: GitHub、GitLab、AWS、Azure
- **通讯服务**: Discord、Telegram
- **可扩展**: 支持自定义模式

## 快速开始

### 1. 安装

```bash
# 克隆项目
git clone https://github.com/your-repo/AIKeySpider.git
cd AIKeySpider

# 运行安装脚本
python setup.py
```

### 2. 基本使用

```bash
# 扫描单个URL
python main.py scan https://example.com

# 扫描多个URL
python main.py scan https://site1.com https://site2.com

# 扫描文本文件
python main.py scan-file example.txt

# 保存结果到文件
python main.py scan https://example.com -o results.json
```

### 3. 配置代理

编辑 `config/proxies.json`:

```json
{
  "proxies": [
    {
      "url": "http://proxy.example.com:8080",
      "type": "http",
      "enabled": true
    }
  ]
}
```

测试代理连接:

```bash
python main.py test-proxy
```

## 配置说明

### 主要配置 (config/settings.json)

```json
{
  "crawler": {
    "max_concurrent_requests": 5,
    "request_timeout": 30,
    "retry_attempts": 3
  },
  "anti_detection": {
    "min_delay": 1.0,
    "max_delay": 5.0,
    "stealth_mode": true
  },
  "patterns": {
    "entropy_threshold": 4.0,
    "min_key_length": 20
  }
}
```

### 搜索查询 (config/search_queries.json)

预定义的搜索查询模板，用于在搜索引擎中查找API Key。

### 代理配置 (config/proxies.json)

代理服务器配置，支持HTTP/HTTPS/SOCKS5代理。

## 技术架构

### 核心模块

- **SmartCrawler**: 智能爬取引擎，API优先，Playwright备选
- **ProxyManager**: 代理管理器，支持轮换和健康检查
- **AntiDetection**: 反爬虫策略，User-Agent轮换、请求伪装
- **PatternMatcher**: 模式匹配器，基于正则和熵值检测

### 爬取策略

```
API调用 > Playwright爬取 > 静态HTTP请求
```

- **GitHub**: 优先GitHub API，备选Playwright处理登录
- **Google搜索**: Playwright处理反爬虫和验证码
- **Pastebin**: 轻量级requests爬取

## API Key模式

### Gemini API Key
- **格式**: `AIza[0-9A-Za-z-_]{35}`
- **示例**: `AIzaSyDaGmWKa4JsXZ-HjGw7ISLan_Tos80ksOE`

### OpenRouter API Key
- **格式**: `sk-or-v1-[a-zA-Z0-9-_]{43,}`
- **示例**: `sk-or-v1-1234567890abcdef1234567890abcdef12345678`

### OpenAI API Key
- **格式**: `sk-[a-zA-Z0-9]{48}`
- **示例**: `sk-1234567890abcdef1234567890abcdef12345678901234`

## 使用示例

### 扫描GitHub仓库

```bash
python main.py scan https://github.com/user/repo
```

### 批量扫描Pastebin

```bash
python main.py scan \
  https://pastebin.com/abc123 \
  https://pastebin.com/def456 \
  -o pastebin_results.json
```

### 扫描本地代码文件

```bash
python main.py scan-file config.py
python main.py scan-file .env
```

## 安全和合规

### 使用原则

- ✅ 仅用于安全研究和漏洞发现
- ✅ 遵守各平台的使用条款和robots.txt
- ✅ 不进行恶意利用或大规模爬取
- ✅ 保护隐私，敏感信息脱敏处理

### 反检测措施

- 智能请求间隔控制(1-5秒随机延迟)
- 代理IP轮换避免封禁
- User-Agent和浏览器指纹随机化
- 模拟真实用户行为模式

## 开发

### 项目结构

```
AIKeySpider/
├── src/
│   ├── core/           # 核心模块
│   ├── engines/        # 爬取引擎
│   ├── utils/          # 工具函数
│   └── models/         # 数据模型
├── config/             # 配置文件
├── tests/              # 测试文件
└── main.py            # 主程序入口
```

### 添加自定义模式

```python
from src.core.patterns import pattern_matcher, APIKeyPattern

# 添加自定义API Key模式
custom_pattern = APIKeyPattern(
    name="Custom API Key",
    pattern=r"custom-[a-zA-Z0-9]{32}",
    context_keywords=["custom", "api"],
    entropy_threshold=4.0
)

pattern_matcher.add_custom_pattern(custom_pattern)
```

## 许可证

本项目仅供安全研究和教育用途使用。使用者需要遵守相关法律法规和平台使用条款。

## 免责声明

本工具仅用于安全研究和漏洞发现，不得用于非法用途。使用者需要对自己的行为负责，开发者不承担任何法律责任。
