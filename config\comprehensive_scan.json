{"scan_name": "Comprehensive API Key Scan", "description": "全面扫描配置示例", "urls": ["https://pastebin.com/raw/example1", "https://gist.githubusercontent.com/user/id/raw/file.txt"], "files": ["test_openrouter.py", "test_sample.txt"], "directories": ["./test_dir"], "github": {"enabled": true, "key_types": ["Gemini API Key", "OpenAI API Key", "GitHub Token"], "max_results": 50, "token": null}, "search_engines": {"enabled": false, "engines": ["google"], "key_types": ["Gemini API Key", "OpenRouter API Key"], "max_results": 10}, "filters": {"min_entropy": 4.0, "min_context_score": 0.0, "exclude_types": [], "include_types": []}, "validation": {"enabled": false, "timeout": 30, "max_concurrent": 3}, "output": {"deduplicate": true, "sort_by": "entropy", "include_context": true, "mask_keys": true}}