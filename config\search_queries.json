{"gemini_queries": ["\"AIza\" api key", "\"AIza\" google gemini", "GOOGLE_API_KEY AIza", "gemini api key AIza", "google ai api key", "\"AIza\" site:github.com", "\"AIza\" site:pastebin.com", "\"AIza\" filetype:env", "\"AIza\" filetype:json", "\"AIza\" filetype:py"], "openrouter_queries": ["\"sk-or-v1\" api key", "openrouter api key", "OPENROUTER_API_KEY", "\"sk-or-v1\" site:github.com", "\"sk-or-v1\" site:pastebin.com", "openrouter token", "\"sk-or-v1\" filetype:env", "\"sk-or-v1\" filetype:json"], "openai_queries": ["\"sk-\" openai api key", "OPENAI_API_KEY sk-", "\"sk-\" gpt api", "\"sk-\" site:github.com", "\"sk-\" site:pastebin.com", "openai api key sk-", "\"sk-\" filetype:env", "\"sk-\" filetype:py"], "anthropic_queries": ["\"sk-ant-api03\" anthropic", "claude api key", "ANTHROPIC_API_KEY", "\"sk-ant-api03\" site:github.com", "\"sk-ant-api03\" site:pastebin.com", "anthropic claude token", "\"sk-ant-api03\" filetype:env"], "huggingface_queries": ["\"hf_\" huggingface token", "HF_TOKEN hf_", "huggingface api token", "\"hf_\" site:github.com", "\"hf_\" site:pastebin.com", "\"hf_\" filetype:env", "\"hf_\" filetype:py"], "github_queries": ["\"ghp_\" github token", "\"gho_\" github token", "\"ghu_\" github token", "\"ghs_\" github token", "GITHUB_TOKEN gh", "personal access token github", "\"gh\" site:pastebin.com", "github token filetype:env"], "aws_queries": ["\"AKIA\" aws access key", "AWS_ACCESS_KEY_ID AKIA", "\"AKIA\" site:github.com", "\"AKIA\" site:pastebin.com", "aws credentials AKIA", "\"AKIA\" filetype:env", "\"AKIA\" filetype:json"], "discord_queries": ["discord bot token", "DISCORD_TOKEN", "discord api token", "\"discord\" \"token\" site:github.com", "\"discord\" \"token\" site:pastebin.com", "discord bot filetype:env", "discord token filetype:py"], "telegram_queries": ["telegram bot token", "TELEGRAM_TOKEN", "telegram api token", "\"telegram\" \"token\" site:github.com", "\"telegram\" \"token\" site:pastebin.com", "telegram bot filetype:env"], "general_queries": ["api key site:github.com", "api token site:pastebin.com", "\"api_key\" filetype:env", "\"API_KEY\" filetype:json", "\"token\" filetype:py", "\"secret\" filetype:env", "credentials filetype:json"]}