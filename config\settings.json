{"crawler": {"max_concurrent_requests": 5, "request_timeout": 30, "retry_attempts": 3, "retry_delay": 2.0, "user_agent_rotation": true, "proxy_rotation": true}, "anti_detection": {"min_delay": 1.0, "max_delay": 5.0, "random_viewport": true, "stealth_mode": true, "session_persistence": true}, "proxy": {"enabled": true, "health_check_interval": 300, "max_failures": 3, "test_url": "http://httpbin.org/ip"}, "patterns": {"entropy_threshold": 4.0, "min_key_length": 20, "context_window": 100, "enable_context_scoring": true}, "output": {"format": "json", "include_context": true, "deduplicate": true, "sort_by_entropy": true}, "logging": {"level": "INFO", "file": "logs/aikey_spider.log", "max_size": "10MB", "backup_count": 5}, "github": {"api_base_url": "https://api.github.com", "search_endpoint": "/search/code", "rate_limit_delay": 60, "max_results_per_query": 100}, "search_engines": {"google": {"enabled": true, "max_results": 50, "safe_search": "off"}, "bing": {"enabled": false, "max_results": 50}}}