#!/usr/bin/env python3
"""
调试模式匹配
"""
import sys
sys.path.insert(0, 'src')

from src.core.patterns import pattern_matcher

# 测试文本
test_text = """
OPENROUTER_API_KEY = "sk-or-v1-1234567890abcdef1234567890abcdef12345678"
INVALID_KEY = "sk-or-v1-invalid1234567890abcdef1234567890abcdef"
os.environ["OPENROUTER_API_KEY"] = "sk-or-v1-abcdef1234567890abcdef1234567890abcdef123456"
"""

print("测试文本:")
print(test_text)
print("\n" + "="*50)

# 查找所有匹配
matches = pattern_matcher.find_api_keys(test_text)

print(f"找到 {len(matches)} 个匹配:")
for i, match in enumerate(matches, 1):
    print(f"\n{i}. {match['type']}")
    print(f"   Key: {match['key']}")
    print(f"   熵值: {match['entropy']:.2f}")
    print(f"   位置: {match['start']}-{match['end']}")
    print(f"   上下文评分: {match['context_score']:.2f}")

# 测试特定的OpenRouter模式
print("\n" + "="*50)
print("测试OpenRouter模式:")

import re
openrouter_pattern = re.compile(r"sk-or-v1-[a-zA-Z0-9\-_]{43,}", re.IGNORECASE)
openrouter_matches = openrouter_pattern.findall(test_text)

print(f"OpenRouter正则匹配: {openrouter_matches}")

# 测试Azure模式
print("\n测试Azure模式:")
azure_pattern = re.compile(r"[a-f0-9]{32}", re.IGNORECASE)
azure_matches = azure_pattern.findall(test_text)

print(f"Azure正则匹配: {azure_matches}")
