2025-08-06 00:46:29,661 - src.core.smart_crawler - INFO - SmartCrawler initialized
2025-08-06 00:46:32,282 - src.core.smart_crawler - INFO - SmartCrawler closed
2025-08-06 01:04:40,053 - src.core.batch_scanner - INFO - Found 2 files to scan in test_dir
2025-08-06 01:11:23,087 - src.core.validator - INFO - API Key validator initialized
2025-08-06 01:15:39,617 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:15:39,618 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:15:40,237 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:40,239 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter api key
2025-08-06 01:15:41,449 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:41,449 - src.engines.github_scanner - INFO - Searching GitHub for: OPENROUTER_API_KEY
2025-08-06 01:15:43,365 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:43,365 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:github.com
2025-08-06 01:15:48,108 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:48,108 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:pastebin.com
2025-08-06 01:15:51,721 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:51,721 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter token
2025-08-06 01:15:55,070 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:55,072 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:env
2025-08-06 01:15:56,881 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:15:56,882 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:json
2025-08-06 01:15:58,315 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:16:05,967 - src.core.smart_crawler - INFO - SmartCrawler initialized
2025-08-06 01:16:08,165 - src.core.smart_crawler - INFO - SmartCrawler closed
2025-08-06 01:16:38,382 - src.core.spider - INFO - Starting comprehensive scan
2025-08-06 01:16:38,382 - src.core.spider - INFO - Starting URL crawl for 2 URLs
2025-08-06 01:16:38,388 - src.core.smart_crawler - INFO - SmartCrawler initialized
2025-08-06 01:16:38,890 - src.core.smart_crawler - WARNING - Requests crawl failed for https://pastebin.com/raw/example1: 400, message='Can not decode content-encoding: brotli (br). Please install `Brotli`', url='https://pastebin.com/raw/example1'
2025-08-06 01:16:38,890 - src.core.smart_crawler - ERROR - Crawl failed for https://pastebin.com/raw/example1: 400, message='Can not decode content-encoding: brotli (br). Please install `Brotli`', url='https://pastebin.com/raw/example1'
2025-08-06 01:16:38,890 - src.core.smart_crawler - INFO - Trying fallback method for https://pastebin.com/raw/example1
2025-08-06 01:16:39,937 - src.core.smart_crawler - INFO - Playwright initialized
2025-08-06 01:16:41,008 - src.core.spider - WARNING - Failed to crawl https://pastebin.com/raw/example1: None
2025-08-06 01:16:41,614 - src.core.spider - WARNING - Failed to crawl https://gist.githubusercontent.com/user/id/raw/file.txt: None
2025-08-06 01:16:41,669 - src.core.smart_crawler - INFO - SmartCrawler closed
2025-08-06 01:16:41,670 - src.core.spider - INFO - Starting file scan for 2 files
2025-08-06 01:16:41,673 - src.core.spider - INFO - Starting directory scan for ./test_dir
2025-08-06 01:16:41,674 - src.core.batch_scanner - INFO - Found 2 files to scan in ./test_dir
2025-08-06 01:16:41,692 - src.core.spider - INFO - Starting GitHub search for ['Gemini API Key', 'OpenAI API Key', 'GitHub Token'] key types
2025-08-06 01:16:41,698 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:16:41,698 - src.engines.github_scanner - INFO - Searching GitHub for: "AIza" api key
2025-08-06 01:16:42,227 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:16:42,228 - src.engines.github_scanner - INFO - Searching GitHub for: "AIza" google gemini
2025-08-06 01:16:44,187 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:16:44,187 - src.engines.github_scanner - INFO - Searching GitHub for: GOOGLE_API_KEY AIza
2025-08-06 01:16:49,081 - src.engines.github_scanner - ERROR - GitHub API error: 401
2025-08-06 01:16:49,082 - src.engines.github_scanner - INFO - Searching GitHub for: gemini api key AIza
2025-08-06 01:16:58,529 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:16:58,529 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:17:19,167 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter api key
2025-08-06 01:17:40,601 - src.engines.github_scanner - INFO - Searching GitHub for: OPENROUTER_API_KEY
2025-08-06 01:17:58,417 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:github.com
2025-08-06 01:18:02,698 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:pastebin.com
2025-08-06 01:18:07,077 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter token
2025-08-06 01:18:20,568 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:env
2025-08-06 01:18:24,880 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:json
2025-08-06 01:18:34,354 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:18:34,358 - src.engines.github_scanner - INFO - Searching GitHub for: "AIza" api key
2025-08-06 01:19:04,156 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:19:04,156 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:19:07,863 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter api key
2025-08-06 01:19:11,501 - src.engines.github_scanner - INFO - Searching GitHub for: OPENROUTER_API_KEY
2025-08-06 01:19:17,382 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:github.com
2025-08-06 01:19:20,827 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:pastebin.com
2025-08-06 01:19:23,363 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter token
2025-08-06 01:19:29,763 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:env
2025-08-06 01:19:31,720 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:json
2025-08-06 01:20:42,777 - src.core.validator - INFO - API Key validator initialized
2025-08-06 01:21:15,820 - src.core.validator - INFO - API Key validator initialized
2025-08-06 01:21:34,265 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:21:34,266 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:21:36,312 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter api key
2025-08-06 01:21:41,625 - src.engines.github_scanner - INFO - Searching GitHub for: OPENROUTER_API_KEY
2025-08-06 01:21:47,075 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:github.com
2025-08-06 01:21:48,246 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" site:pastebin.com
2025-08-06 01:21:52,864 - src.engines.github_scanner - INFO - Searching GitHub for: openrouter token
2025-08-06 01:21:59,691 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:env
2025-08-06 01:22:01,796 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" filetype:json
2025-08-06 01:23:10,172 - src.core.validator - INFO - API Key validator initialized
2025-08-06 01:26:03,225 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:26:03,226 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:27:46,583 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:27:46,584 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:31:49,766 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:31:49,766 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
2025-08-06 01:35:02,419 - src.engines.github_scanner - INFO - GitHub scanner initialized
2025-08-06 01:35:02,420 - src.engines.github_scanner - INFO - Searching GitHub for: "sk-or-v1" api key
