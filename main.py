#!/usr/bin/env python3
"""
AIKeySpider - API Key爬取工具主程序
"""
import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import List, Dict

import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.smart_crawler import SmartCrawler
from src.core.patterns import pattern_matcher
from src.core.proxy_manager import proxy_manager
from src.core.anti_detection import anti_detection
from src.core.batch_scanner import BatchScanner
from src.core.spider import APIKeySpider
from src.core.validator import APIKeyValidator
from src.engines.github_scanner import GitHubScanner
from src.engines.search_engine import SearchEngineManager
from src.models.result import ScanResult, APIKeyMatch

console = Console()


def setup_logging(level: str = "INFO"):
    """设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "aikey_spider.log"),
            logging.StreamHandler()
        ]
    )


def load_config(config_path: str = "config/settings.json") -> Dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        console.print(f"[red]配置文件未找到: {config_path}[/red]")
        return {}
    except json.JSONDecodeError as e:
        console.print(f"[red]配置文件格式错误: {e}[/red]")
        return {}


def load_proxies():
    """加载代理配置"""
    proxy_file = "config/proxies.json"
    try:
        with open(proxy_file, 'r', encoding='utf-8') as f:
            proxy_config = json.load(f)
            
        for proxy_info in proxy_config.get("proxies", []):
            if proxy_info.get("enabled", False):
                proxy_manager.add_proxy(
                    proxy_info["url"],
                    proxy_info.get("type", "http"),
                    proxy_info.get("username"),
                    proxy_info.get("password")
                )
        
        console.print(f"[green]已加载 {len(proxy_manager.proxies)} 个代理[/green]")
        
    except FileNotFoundError:
        console.print("[yellow]代理配置文件未找到，将不使用代理[/yellow]")
    except Exception as e:
        console.print(f"[red]加载代理配置失败: {e}[/red]")


async def scan_text(text: str, source: str = "unknown") -> List[Dict]:
    """扫描文本中的API Key"""
    matches = pattern_matcher.find_api_keys(text)
    
    # 添加来源信息
    for match in matches:
        match['source'] = source
    
    return matches


async def scan_url(url: str) -> List[Dict]:
    """扫描单个URL"""
    async with SmartCrawler() as crawler:
        result = await crawler.crawl(url)
        
        if result.success:
            matches = await scan_text(result.content, url)
            console.print(f"[green]✓[/green] {url} - 找到 {len(matches)} 个潜在API Key")
            return matches
        else:
            console.print(f"[red]✗[/red] {url} - 爬取失败: {result.error}")
            return []


async def scan_urls(urls: List[str]) -> List[Dict]:
    """批量扫描URL"""
    all_matches = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("扫描URL中...", total=len(urls))
        
        async with SmartCrawler() as crawler:
            for url in urls:
                try:
                    result = await crawler.crawl(url)
                    if result.success:
                        matches = await scan_text(result.content, url)
                        all_matches.extend(matches)
                        progress.console.print(f"[green]✓[/green] {url} - 找到 {len(matches)} 个潜在API Key")
                    else:
                        progress.console.print(f"[red]✗[/red] {url} - 爬取失败")
                except Exception as e:
                    progress.console.print(f"[red]✗[/red] {url} - 异常: {e}")
                
                progress.advance(task)
    
    return all_matches


def display_results(matches: List[Dict]):
    """显示扫描结果"""
    if not matches:
        console.print("[yellow]未找到任何API Key[/yellow]")
        return
    
    # 按类型分组
    by_type = {}
    for match in matches:
        key_type = match['type']
        if key_type not in by_type:
            by_type[key_type] = []
        by_type[key_type].append(match)
    
    # 创建结果表格
    table = Table(title="发现的API Key")
    table.add_column("类型", style="cyan")
    table.add_column("Key", style="yellow")
    table.add_column("熵值", style="green")
    table.add_column("来源", style="blue")
    table.add_column("上下文评分", style="magenta")
    
    for key_type, type_matches in by_type.items():
        for match in type_matches:
            # 截断显示长Key
            key_display = match['key']
            if len(key_display) > 50:
                key_display = key_display[:25] + "..." + key_display[-22:]
            
            table.add_row(
                key_type,
                key_display,
                f"{match['entropy']:.2f}",
                match['source'],
                f"{match.get('context_score', 0):.2f}"
            )
    
    console.print(table)
    
    # 显示统计信息
    console.print(f"\n[bold green]总计找到 {len(matches)} 个潜在API Key[/bold green]")
    for key_type, type_matches in by_type.items():
        console.print(f"  {key_type}: {len(type_matches)} 个")


def save_results(matches: List[Dict], output_file: str, show_message: bool = False):
    """保存结果到文件"""
    try:
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 添加时间戳和统计信息
        result_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_matches": len(matches),
            "unique_keys": len(set(match.get('key', '') for match in matches)),
            "matches": matches
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)

        if show_message:
            console.print(f"[green]结果已保存到: {output_path.absolute()}[/green]")
            console.print(f"[blue]当前共发现 {len(matches)} 个API Key[/blue]")
    except Exception as e:
        console.print(f"[red]保存结果失败: {e}[/red]")


@click.group()
@click.option('--config', default='config/settings.json', help='配置文件路径')
@click.option('--log-level', default='INFO', help='日志级别')
@click.pass_context
def cli(ctx, config, log_level):
    """AIKeySpider - API Key爬取工具"""
    ctx.ensure_object(dict)
    ctx.obj['config'] = load_config(config)
    setup_logging(log_level)
    load_proxies()


@cli.command()
@click.argument('urls', nargs=-1, required=True)
@click.option('--output', '-o', help='输出文件路径')
@click.pass_context
def scan(ctx, urls, output):
    """扫描指定URL中的API Key"""
    console.print(f"[bold blue]开始扫描 {len(urls)} 个URL...[/bold blue]")
    
    # 运行异步扫描
    matches = asyncio.run(scan_urls(list(urls)))
    
    # 显示结果
    display_results(matches)
    
    # 保存结果
    if output:
        save_results(matches, output)


@cli.command()
@click.argument('text_file')
@click.option('--output', '-o', help='输出文件路径')
@click.pass_context
def scan_file(ctx, text_file, output):
    """扫描文本文件中的API Key"""
    try:
        with open(text_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        console.print(f"[bold blue]扫描文件: {text_file}[/bold blue]")
        
        # 扫描文本
        matches = asyncio.run(scan_text(content, text_file))
        
        # 显示结果
        display_results(matches)
        
        # 保存结果
        if output:
            save_results(matches, output)
            
    except FileNotFoundError:
        console.print(f"[red]文件未找到: {text_file}[/red]")
    except Exception as e:
        console.print(f"[red]扫描文件失败: {e}[/red]")


@cli.command()
@click.option('--key-types', help='API Key类型，用逗号分隔')
@click.option('--max-results', default=50, help='最大结果数量')
@click.option('--output', '-o', help='输出文件路径')
@click.option('--token', help='GitHub API Token')
@click.pass_context
def search_github(ctx, key_types, max_results, output, token):
    """在GitHub中搜索API Key"""
    console.print("[bold blue]开始GitHub搜索...[/bold blue]")

    if output:
        output_path = Path(output).absolute()
        console.print(f"[yellow]结果将实时保存到: {output_path}[/yellow]")

    # 解析key_types
    target_types = None
    if key_types:
        target_types = [t.strip() for t in key_types.split(',')]

    async def run_github_search():
        matches = []

        async with GitHubScanner(token) as scanner:
            async for match in scanner.search_api_keys(target_types, max_results):
                matches.append(match)
                console.print(f"[green]发现[/green] {match['type']} 在 {match['repository']}")

                # 实时保存结果
                if output:
                    save_results(matches, output, show_message=False)

        return matches

    # 运行搜索
    matches = asyncio.run(run_github_search())

    # 显示结果
    display_results(matches)

    # 最终保存结果
    if output:
        save_results(matches, output, show_message=True)


@cli.command()
@click.option('--key-types', help='API Key类型，用逗号分隔')
@click.option('--max-results', default=20, help='每个查询的最大结果数量')
@click.option('--engines', default='google', help='搜索引擎，用逗号分隔')
@click.option('--output', '-o', help='输出文件路径')
@click.pass_context
def search_engine(ctx, key_types, max_results, engines, output):
    """通过搜索引擎搜索API Key"""
    console.print("[bold blue]开始搜索引擎搜索...[/bold blue]")

    # 解析参数
    target_types = None
    if key_types:
        target_types = [t.strip() for t in key_types.split(',')]

    engine_list = [e.strip() for e in engines.split(',')]

    async def run_search_engine():
        matches = []

        async with SearchEngineManager() as manager:
            async for match in manager.search_api_keys(target_types, max_results, engine_list):
                matches.append(match)
                console.print(f"[green]发现[/green] {match['type']} 在 {match['page_url']}")

                # 实时保存结果
                if output:
                    save_results(matches, output)

        return matches

    # 运行搜索
    matches = asyncio.run(run_search_engine())

    # 显示结果
    display_results(matches)

    # 保存结果
    if output:
        save_results(matches, output)


@cli.command()
@click.argument('repo_name')
@click.option('--output', '-o', help='输出文件路径')
@click.option('--token', help='GitHub API Token')
@click.pass_context
def scan_repo(ctx, repo_name, output, token):
    """扫描GitHub仓库"""
    console.print(f"[bold blue]扫描GitHub仓库: {repo_name}[/bold blue]")

    async def run_repo_scan():
        matches = []

        async with GitHubScanner(token) as scanner:
            async for match in scanner.scan_repository_files(repo_name):
                matches.append(match)
                console.print(f"[green]发现[/green] {match['type']} 在 {match['file_path']}")

        return matches

    # 运行扫描
    matches = asyncio.run(run_repo_scan())

    # 显示结果
    display_results(matches)

    # 保存结果
    if output:
        save_results(matches, output)


@cli.command()
@click.pass_context
def test_proxy(ctx):
    """测试代理连接"""
    if not proxy_manager.proxies:
        console.print("[yellow]未配置任何代理[/yellow]")
        return

    console.print("[bold blue]测试代理连接...[/bold blue]")

    async def test_all_proxies():
        tasks = [proxy_manager.test_proxy(proxy) for proxy in proxy_manager.proxies]
        await asyncio.gather(*tasks, return_exceptions=True)

    asyncio.run(test_all_proxies())

    # 显示统计
    stats = proxy_manager.get_proxy_stats()
    console.print(f"代理统计: {stats['working']}/{stats['total']} 可用 "
                 f"(成功率: {stats['success_rate']:.1%})")


@cli.command()
@click.argument('directory')
@click.option('--output', '-o', help='输出文件路径')
@click.option('--max-concurrent', default=10, help='最大并发数')
@click.option('--file-patterns', help='文件模式，用逗号分隔')
@click.pass_context
def scan_dir(ctx, directory, output, max_concurrent, file_patterns):
    """递归扫描目录中的文件"""
    console.print(f"[bold blue]扫描目录: {directory}[/bold blue]")

    # 解析文件模式
    patterns = None
    if file_patterns:
        patterns = [p.strip() for p in file_patterns.split(',')]

    async def run_directory_scan():
        matches = []

        async def progress_callback(source, match_count, error):
            if error:
                console.print(f"[red]✗[/red] {source} - 错误: {error}")
            else:
                console.print(f"[green]✓[/green] {source} - 找到 {match_count} 个API Key")

        async with BatchScanner(max_concurrent=max_concurrent) as scanner:
            async for match in scanner.scan_directory_recursive(
                directory, patterns, progress_callback=progress_callback
            ):
                matches.append(match)

        return matches, scanner.get_performance_stats()

    # 运行扫描
    matches, stats = asyncio.run(run_directory_scan())

    # 显示结果
    display_results(matches)

    # 显示性能统计
    console.print(f"\n[bold green]性能统计:[/bold green]")
    console.print(f"扫描文件: {stats['total_scanned']} 个")
    console.print(f"发现API Key: {stats['total_matches']} 个")
    console.print(f"错误数量: {stats['total_errors']} 个")
    console.print(f"扫描时间: {stats['duration_seconds']:.2f} 秒")
    console.print(f"扫描速度: {stats['scan_rate']:.2f} 文件/秒")

    # 保存结果
    if output:
        save_results(matches, output)


@cli.command()
@click.argument('urls', nargs=-1, required=True)
@click.option('--output', '-o', help='输出文件路径')
@click.option('--max-concurrent', default=5, help='最大并发数')
@click.pass_context
def scan_batch(ctx, urls, output, max_concurrent):
    """批量扫描URL（高性能版本）"""
    console.print(f"[bold blue]批量扫描 {len(urls)} 个URL...[/bold blue]")

    async def run_batch_scan():
        matches = []

        async def progress_callback(source, match_count, error):
            if error:
                console.print(f"[red]✗[/red] {source} - 错误: {error}")
            else:
                console.print(f"[green]✓[/green] {source} - 找到 {match_count} 个API Key")

        async with BatchScanner(max_concurrent=max_concurrent) as scanner:
            async for match in scanner.scan_urls_batch(
                list(urls), progress_callback=progress_callback
            ):
                matches.append(match)

        return matches, scanner.get_performance_stats()

    # 运行扫描
    matches, stats = asyncio.run(run_batch_scan())

    # 显示结果
    display_results(matches)

    # 显示性能统计
    console.print(f"\n[bold green]性能统计:[/bold green]")
    console.print(f"扫描URL: {stats['total_scanned']} 个")
    console.print(f"发现API Key: {stats['total_matches']} 个")
    console.print(f"错误数量: {stats['total_errors']} 个")
    console.print(f"扫描时间: {stats['duration_seconds']:.2f} 秒")
    console.print(f"扫描速度: {stats['scan_rate']:.2f} URL/秒")

    # 保存结果
    if output:
        save_results(matches, output)


@cli.command()
@click.pass_context
def list_patterns(ctx):
    """列出支持的API Key模式"""
    console.print("[bold blue]支持的API Key类型:[/bold blue]")

    patterns = pattern_matcher.get_pattern_names()

    table = Table(title="API Key模式")
    table.add_column("序号", style="cyan")
    table.add_column("类型", style="yellow")
    table.add_column("描述", style="green")

    descriptions = {
        "Gemini API Key": "Google Gemini AI API密钥",
        "OpenRouter API Key": "OpenRouter API密钥",
        "OpenAI API Key": "OpenAI GPT API密钥",
        "Anthropic API Key": "Anthropic Claude API密钥",
        "GitHub Token": "GitHub个人访问令牌",
        "AWS Access Key": "AWS访问密钥",
        "Discord Bot Token": "Discord机器人令牌",
        "Telegram Bot Token": "Telegram机器人令牌",
        "Stripe API Key": "Stripe支付API密钥",
        "SendGrid API Key": "SendGrid邮件API密钥",
        "Slack Bot Token": "Slack机器人令牌",
        "JWT Token": "JSON Web Token",
        "MongoDB Connection String": "MongoDB连接字符串"
    }

    for i, pattern_name in enumerate(patterns, 1):
        description = descriptions.get(pattern_name, "API密钥")
        table.add_row(str(i), pattern_name, description)

    console.print(table)


@cli.command()
@click.option('--config-file', help='扫描配置文件路径')
@click.option('--validate', is_flag=True, help='验证发现的API Key')
@click.option('--output', '-o', help='输出文件路径')
@click.option('--format', 'output_format', default='json', help='输出格式 (json/csv)')
@click.pass_context
def comprehensive(ctx, config_file, validate, output, output_format):
    """综合扫描 - 根据配置文件执行多种扫描"""
    if not config_file:
        console.print("[red]请提供配置文件路径[/red]")
        return

    # 加载扫描配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            scan_config = json.load(f)
    except Exception as e:
        console.print(f"[red]加载配置文件失败: {e}[/red]")
        return

    console.print("[bold blue]开始综合扫描...[/bold blue]")

    async def run_comprehensive_scan():
        async with APIKeySpider(scan_config) as spider:
            matches = await spider.comprehensive_scan(scan_config, validate_keys=validate)
            stats = spider.get_statistics()
            return matches, stats

    # 运行扫描
    matches, stats = asyncio.run(run_comprehensive_scan())

    # 创建结果对象
    scan_result = ScanResult()
    for match_dict in matches:
        api_match = APIKeyMatch.from_dict(match_dict)
        scan_result.add_match(api_match)
    scan_result.finish_scan()

    # 显示结果
    display_results(matches)

    # 显示统计信息
    console.print(f"\n[bold green]综合统计:[/bold green]")
    console.print(f"扫描来源: {stats['total_sources']} 个")
    console.print(f"发现API Key: {stats['total_matches']} 个")
    console.print(f"唯一Key: {stats['unique_keys']} 个")
    console.print(f"Key类型: {stats['key_types_found']} 种")
    console.print(f"成功率: {stats['success_rate']:.1%}")

    # 保存结果
    if output:
        if output_format.lower() == 'csv':
            scan_result.export_csv(output)
        else:
            scan_result.export_json(output)
        console.print(f"[green]结果已保存到: {output}[/green]")


@cli.command()
@click.argument('api_key')
@click.argument('key_type')
@click.pass_context
def validate(ctx, api_key, key_type):
    """验证单个API Key"""
    console.print(f"[bold blue]验证 {key_type}: {api_key[:10]}...[/bold blue]")

    async def run_validation():
        async with APIKeyValidator() as validator:
            return await validator.validate_key(api_key, key_type)

    # 运行验证
    result = asyncio.run(run_validation())

    # 显示结果
    if result['valid'] is True:
        console.print(f"[green]✓ API Key有效[/green]")
        if result.get('permissions'):
            console.print(f"权限: {', '.join(result['permissions'][:5])}")
        if result.get('rate_limit'):
            console.print(f"速率限制: {result['rate_limit']}")
    elif result['valid'] is False:
        console.print(f"[red]✗ API Key无效[/red]")
        if result.get('error'):
            console.print(f"错误: {result['error']}")
    else:
        console.print(f"[yellow]? 无法验证[/yellow]")
        if result.get('error'):
            console.print(f"错误: {result['error']}")


if __name__ == "__main__":
    cli()
