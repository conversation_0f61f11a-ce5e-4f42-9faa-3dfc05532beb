{"timestamp": "2025-08-06 01:32:30", "total_matches": 10, "unique_keys": 10, "matches": [{"key": "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", "start": 0, "end": 73, "type": "OpenRouter API Key", "entropy": 4.221019429936298, "context": "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\n\nexport OPENROUTER_API_KEY=sk-or-v1-bc3fc7467e251bf7e8a83fb069bd52ea7a44f5785c6b4e2f5cd3f103c2dcf64", "context_score": 1.0, "source": "github", "repository": "shidalgo0925/openrouter_chat", "file_path": "Api_key.txt", "file_url": "https://github.com/shidalgo0925/openrouter_chat/blob/6447cc365b39a087e6634a5a391fafe9d1240edf/Api_key.txt", "repository_url": "https://github.com/shidalgo0925/openrouter_chat", "last_modified": "", "file_size": 0}, {"key": "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", "start": 1025, "end": 1098, "type": "OpenRouter API Key", "entropy": 4.196712066774416, "context": "<PERSON>\n  <PERSON> <PERSON><PERSON> (Female): Z3R5wn05IrDiVCyEkUrK\n\n## AI Models Configuration\n- **OpenRouter API Key**: 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\n- **DeepSeek API Key**: ***********************************\n- **Fal.ai API Key**: f6acc575-f48f-410", "context_score": 0.0, "source": "github", "repository": "bakiel/kfar-shop", "file_path": "API_KEYS_SECURE.md", "file_url": "https://github.com/bakiel/kfar-shop/blob/f46ad8b9c138a4666ca4c904fc530051e4bb0d29/API_KEYS_SECURE.md", "repository_url": "https://github.com/bakiel/kfar-shop", "last_modified": "", "file_size": 0}, {"key": "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", "start": 1160, "end": 1233, "type": "OpenRouter API Key", "entropy": 4.252520750625743, "context": " words){\r\n  const openai = new OpenAI({\r\n    baseURL: 'https://openrouter.ai/api/v1',\r\n    apiKey: '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',\r\n  });\r\n\r\n  async function main() {\r\n    const completion = await openai.chat.completions.create(", "context_score": 0.0, "source": "github", "repository": "Arpan2411/ChatBot", "file_path": "api.js", "file_url": "https://github.com/Arpan2411/ChatBot/blob/c86b0bffa2f79535e7a161d478ddc80b80dfd9f9/api.js", "repository_url": "https://github.com/Arpan2411/ChatBot", "last_modified": "", "file_size": 0}, {"key": "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", "start": 1847, "end": 1920, "type": "OpenRouter API Key", "entropy": 4.101032394105722, "context": " words){\r\n  const openai = new OpenAI({\r\n    baseURL: \"https://openrouter.ai/api/v1\",\r\n    apiKey: \"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\",\r\n  });\r\n  async function main() {\r\n    const completion = await openai.chat.completions.create({\r", "context_score": 0.0, "source": "github", "repository": "Arpan2411/ChatBot", "file_path": "api.js", "file_url": "https://github.com/Arpan2411/ChatBot/blob/c86b0bffa2f79535e7a161d478ddc80b80dfd9f9/api.js", "repository_url": "https://github.com/Arpan2411/ChatBot", "last_modified": "", "file_size": 0}, {"key": "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", "start": 3932, "end": 4005, "type": "OpenRouter API Key", "entropy": 4.01606698447725, "context": "words);\r\n//let gemini = geminiAns.length;\r\n\r\nconsole.log(geminiAns);*/\r\n//console.log(gemini);\r\n\r\n//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\r\n\r\n\r\n//Gemini code starts\r\n/*const genAI = new GoogleGenerativeAI(\"AIzaSyB4kFPhiNTPNnioQNxsxg5AwLkc", "context_score": 0.0, "source": "github", "repository": "Arpan2411/ChatBot", "file_path": "api.js", "file_url": "https://github.com/Arpan2411/ChatBot/blob/c86b0bffa2f79535e7a161d478ddc80b80dfd9f9/api.js", "repository_url": "https://github.com/Arpan2411/ChatBot", "last_modified": "", "file_size": 0}, {"key": "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", "start": 3590, "end": 3663, "type": "OpenRouter API Key", "entropy": 4.093446991213478, "context": "\n# @app.before_request\n# def before_request():\n#     g.chatbot = ContextAwareGeminiChatbot(api_key=\"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\", model=\"google/gemma-7b-it:free\")\n\***********('/', methods=[\"GET\", \"POST\"])\***********('/', method", "context_score": 0.0, "source": "github", "repository": "patidarumesh/AI-ENABLED_AGRICULTURE-main", "file_path": "api.py", "file_url": "https://github.com/patidarumesh/AI-ENABLED_AGRICULTURE-main/blob/a408770d37d896bc45d4e88c6a9beb3b53e1c582/api.py", "repository_url": "https://github.com/patidarumesh/AI-ENABLED_AGRICULTURE-main", "last_modified": "", "file_size": 0}, {"key": "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", "start": 1873, "end": 1946, "type": "OpenRouter API Key", "entropy": 4.2185049993877675, "context": ".\n */\n\n //api key 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\napi key 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\n\n\n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\n\n --------/-\n git statu", "context_score": 0.0, "source": "github", "repository": "egla24330/E-commerce", "file_path": "api_doc.txt", "file_url": "https://github.com/egla24330/E-commerce/blob/e8c19914f85f00c7263b39fccbdf58821f23c92b/api_doc.txt", "repository_url": "https://github.com/egla24330/E-commerce", "last_modified": "", "file_size": 0}, {"key": "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", "start": 1791, "end": 1864, "type": "OpenRouter API Key", "entropy": 4.185444080047118, "context": "rovides methods for user authentication, including login, logout, and registration.\n */\n\n //api key 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\napi key 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\n\n\nsk-or-v1-dce2a1", "context_score": 0.0, "source": "github", "repository": "egla24330/E-commerce", "file_path": "api_doc.txt", "file_url": "https://github.com/egla24330/E-commerce/blob/e8c19914f85f00c7263b39fccbdf58821f23c92b/api_doc.txt", "repository_url": "https://github.com/egla24330/E-commerce", "last_modified": "", "file_size": 0}, {"key": "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", "start": 1949, "end": 2022, "type": "OpenRouter API Key", "entropy": 4.093581499678676, "context": "f74d5a3188e8d66\napi key 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\n\n\n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\n\n --------/-\n git status            \ngit add .             \ngit commit -m \"fix add btn issue\" \ngit ", "context_score": 0.0, "source": "github", "repository": "egla24330/E-commerce", "file_path": "api_doc.txt", "file_url": "https://github.com/egla24330/E-commerce/blob/e8c19914f85f00c7263b39fccbdf58821f23c92b/api_doc.txt", "repository_url": "https://github.com/egla24330/E-commerce", "last_modified": "", "file_size": 0}, {"key": "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", "start": 28, "end": 101, "type": "OpenRouter API Key", "entropy": 4.241128074105619, "context": "import requests\n\nAPI_KEY = \"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\"\nHEADERS = {\n    \"Authorization\": f\"Bearer {API_KEY}\",\n    \"Content-Type\": \"application/json\",\n    ", "context_score": 0.0, "source": "github", "repository": "zhu-ziyu/G11CSFPT", "file_path": "API.py", "file_url": "https://github.com/zhu-ziyu/G11CSFPT/blob/b46976f8df6374355c71ac8e501527cc390f0390/API.py", "repository_url": "https://github.com/zhu-ziyu/G11CSFPT", "last_modified": "", "file_size": 0}]}