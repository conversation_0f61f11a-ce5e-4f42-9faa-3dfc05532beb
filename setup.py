#!/usr/bin/env python3
"""
AIKeySpider安装脚本
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"正在{description}...")
    print(f"命令: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {description}成功")
        if result.stdout:
            print("输出:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description}失败")
        print("错误:", e.stderr)
        return False


def install_dependencies():
    """安装Python依赖"""
    print("开始安装AIKeySpider...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    print(f"Python版本: {sys.version}")
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装Python依赖"):
        return False
    
    # 安装Playwright浏览器
    if not run_command("playwright install chromium", "安装Playwright浏览器"):
        print("警告: Playwright浏览器安装失败，某些功能可能不可用")
    
    return True


def create_directories():
    """创建必要的目录"""
    directories = ["logs", "output", "data"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ 创建目录: {directory}")


def setup_environment():
    """设置环境"""
    # 创建.env文件模板
    env_template = """# AIKeySpider环境变量配置
# GitHub API Token (可选，用于提高API限制)
# GITHUB_TOKEN=your_github_token_here

# 代理设置 (可选)
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=http://proxy.example.com:8080

# 日志级别
LOG_LEVEL=INFO

# 输出目录
OUTPUT_DIR=output
"""
    
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_template)
        print("✓ 创建环境变量模板文件: .env")
    else:
        print("✓ 环境变量文件已存在")


def main():
    """主安装函数"""
    print("AIKeySpider 安装程序")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("requirements.txt").exists():
        print("错误: 请在项目根目录运行此脚本")
        return False
    
    # 创建目录
    create_directories()
    
    # 设置环境
    setup_environment()
    
    # 安装依赖
    if not install_dependencies():
        print("\n安装失败！请检查错误信息。")
        return False
    
    print("\n" + "=" * 50)
    print("✓ AIKeySpider 安装完成！")
    print("=" * 50)
    
    print("\n使用方法:")
    print("1. 扫描URL:")
    print("   python main.py scan https://example.com")
    print("\n2. 扫描文件:")
    print("   python main.py scan-file example.txt")
    print("\n3. 测试代理:")
    print("   python main.py test-proxy")
    print("\n4. 查看帮助:")
    print("   python main.py --help")
    
    print("\n配置文件:")
    print("- config/settings.json - 主要设置")
    print("- config/proxies.json - 代理配置")
    print("- .env - 环境变量")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
