"""
反爬虫策略模块 - 实现User-Agent轮换、请求间隔控制等反检测机制
"""
import random
import time
import asyncio
from typing import Dict, List, Optional
from fake_useragent import UserAgent
import logging

logger = logging.getLogger(__name__)


class AntiDetection:
    """反爬虫检测策略"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.last_request_time = {}
        self.min_delay = 1.0  # 最小延迟1秒
        self.max_delay = 5.0  # 最大延迟5秒
        self.session_cookies = {}
        
        # 预定义的User-Agent列表（备选）
        self.fallback_user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        try:
            # 优先使用fake-useragent
            return self.ua.random
        except Exception as e:
            logger.warning(f"Failed to get random user agent: {e}")
            # 使用备选列表
            return random.choice(self.fallback_user_agents)
    
    def get_browser_user_agent(self, browser: str = None) -> str:
        """获取指定浏览器的User-Agent"""
        try:
            if browser == 'chrome':
                return self.ua.chrome
            elif browser == 'firefox':
                return self.ua.firefox
            elif browser == 'safari':
                return self.ua.safari
            else:
                return self.ua.random
        except Exception:
            return random.choice(self.fallback_user_agents)
    
    def get_common_headers(self, referer: str = None) -> Dict[str, str]:
        """获取常用的HTTP头"""
        headers = {
            'User-Agent': self.get_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        if referer:
            headers['Referer'] = referer
            
        return headers
    
    async def smart_delay(self, domain: str = "default"):
        """智能延迟 - 根据域名和历史请求调整延迟时间"""
        current_time = time.time()
        last_time = self.last_request_time.get(domain, 0)
        
        # 计算需要等待的时间
        elapsed = current_time - last_time
        base_delay = random.uniform(self.min_delay, self.max_delay)
        
        # 如果距离上次请求时间太短，增加延迟
        if elapsed < base_delay:
            wait_time = base_delay - elapsed
            logger.debug(f"Waiting {wait_time:.2f}s for domain {domain}")
            await asyncio.sleep(wait_time)
        
        self.last_request_time[domain] = time.time()
    
    def set_delay_range(self, min_delay: float, max_delay: float):
        """设置延迟范围"""
        self.min_delay = min_delay
        self.max_delay = max_delay
        logger.info(f"Delay range set to {min_delay}-{max_delay} seconds")
    
    def get_playwright_context_options(self, proxy_config: Dict = None) -> Dict:
        """获取Playwright浏览器上下文选项"""
        options = {
            'user_agent': self.get_random_user_agent(),
            'viewport': {'width': 1920, 'height': 1080},
            'locale': 'en-US',
            'timezone_id': 'America/New_York',
            'permissions': [],
            'extra_http_headers': {
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
            }
        }
        
        if proxy_config:
            options['proxy'] = proxy_config
            
        return options
    
    def get_stealth_options(self) -> Dict:
        """获取隐身模式选项"""
        return {
            'java_enabled': False,
            'webgl_vendor': 'Intel Inc.',
            'webgl_renderer': 'Intel Iris OpenGL Engine',
            'languages': ['en-US', 'en'],
            'plugins': [
                'Chrome PDF Plugin',
                'Chrome PDF Viewer',
                'Native Client'
            ]
        }
    
    def randomize_viewport(self) -> Dict[str, int]:
        """随机化视口大小"""
        width = random.choice([1366, 1440, 1920, 1536, 1280])
        height = random.choice([768, 900, 1080, 864, 720])
        return {'width': width, 'height': height}
    
    def simulate_human_behavior(self) -> Dict:
        """模拟人类行为参数"""
        return {
            'mouse_move_delay': random.uniform(0.1, 0.3),
            'click_delay': random.uniform(0.1, 0.5),
            'type_delay': random.uniform(0.05, 0.15),
            'scroll_delay': random.uniform(0.2, 0.8)
        }
    
    def get_session_cookies(self, domain: str) -> Dict:
        """获取会话Cookie"""
        return self.session_cookies.get(domain, {})
    
    def save_session_cookies(self, domain: str, cookies: Dict):
        """保存会话Cookie"""
        self.session_cookies[domain] = cookies
        logger.debug(f"Saved {len(cookies)} cookies for domain {domain}")
    
    def clear_session_cookies(self, domain: str = None):
        """清除会话Cookie"""
        if domain:
            self.session_cookies.pop(domain, None)
            logger.debug(f"Cleared cookies for domain {domain}")
        else:
            self.session_cookies.clear()
            logger.debug("Cleared all session cookies")


# 全局反检测实例
anti_detection = AntiDetection()
