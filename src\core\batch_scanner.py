"""
批量扫描器 - 高性能批量扫描API Key
"""
import asyncio
import time
from typing import List, Dict, Optional, AsyncGenerator, Callable
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor
import aiofiles

from .smart_crawler import SmartCrawler
from .patterns import pattern_matcher
from .anti_detection import anti_detection

logger = logging.getLogger(__name__)


class BatchScanner:
    """批量扫描器"""
    
    def __init__(self, max_concurrent: int = 10, max_workers: int = 4):
        self.max_concurrent = max_concurrent
        self.max_workers = max_workers
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.stats = {
            'total_scanned': 0,
            'total_matches': 0,
            'total_errors': 0,
            'start_time': 0,
            'end_time': 0
        }
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.stats['start_time'] = time.time()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        self.stats['end_time'] = time.time()
        self.executor.shutdown(wait=True)
    
    async def scan_urls_batch(self, urls: List[str], 
                            progress_callback: Callable = None) -> AsyncGenerator[Dict, None]:
        """批量扫描URL"""
        async def scan_single_url(url: str) -> List[Dict]:
            async with self.semaphore:
                try:
                    async with SmartCrawler() as crawler:
                        result = await crawler.crawl(url)
                        
                        if result.success:
                            matches = pattern_matcher.find_api_keys(result.content)
                            for match in matches:
                                match['source'] = url
                                match['scan_method'] = 'url_batch'
                            
                            self.stats['total_scanned'] += 1
                            self.stats['total_matches'] += len(matches)
                            
                            if progress_callback:
                                await progress_callback(url, len(matches), None)
                            
                            return matches
                        else:
                            self.stats['total_errors'] += 1
                            if progress_callback:
                                await progress_callback(url, 0, result.error)
                            return []
                            
                except Exception as e:
                    self.stats['total_errors'] += 1
                    logger.error(f"Error scanning {url}: {e}")
                    if progress_callback:
                        await progress_callback(url, 0, str(e))
                    return []
        
        # 创建任务
        tasks = [scan_single_url(url) for url in urls]
        
        # 批量执行
        for coro in asyncio.as_completed(tasks):
            matches = await coro
            for match in matches:
                yield match
    
    async def scan_files_batch(self, file_paths: List[str],
                             progress_callback: Callable = None) -> AsyncGenerator[Dict, None]:
        """批量扫描文件"""
        async def scan_single_file(file_path: str) -> List[Dict]:
            try:
                # 异步读取文件
                async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = await f.read()
                
                # 在线程池中执行模式匹配（CPU密集型任务）
                loop = asyncio.get_event_loop()
                matches = await loop.run_in_executor(
                    self.executor, 
                    pattern_matcher.find_api_keys, 
                    content
                )
                
                for match in matches:
                    match['source'] = file_path
                    match['scan_method'] = 'file_batch'
                
                self.stats['total_scanned'] += 1
                self.stats['total_matches'] += len(matches)
                
                if progress_callback:
                    await progress_callback(file_path, len(matches), None)
                
                return matches
                
            except Exception as e:
                self.stats['total_errors'] += 1
                logger.error(f"Error scanning file {file_path}: {e}")
                if progress_callback:
                    await progress_callback(file_path, 0, str(e))
                return []
        
        # 分批处理文件
        batch_size = self.max_concurrent
        for i in range(0, len(file_paths), batch_size):
            batch = file_paths[i:i + batch_size]
            tasks = [scan_single_file(file_path) for file_path in batch]
            
            for coro in asyncio.as_completed(tasks):
                matches = await coro
                for match in matches:
                    yield match
    
    async def scan_directory_recursive(self, directory: str, 
                                     file_patterns: List[str] = None,
                                     exclude_patterns: List[str] = None,
                                     progress_callback: Callable = None) -> AsyncGenerator[Dict, None]:
        """递归扫描目录"""
        if file_patterns is None:
            file_patterns = [
                '*.py', '*.js', '*.json', '*.yaml', '*.yml', 
                '*.env', '*.config', '*.conf', '*.ini', '*.txt'
            ]
        
        if exclude_patterns is None:
            exclude_patterns = [
                '*/node_modules/*', '*/.git/*', '*/venv/*', 
                '*/env/*', '*/__pycache__/*', '*/dist/*', '*/build/*'
            ]
        
        # 收集文件
        file_paths = []
        directory_path = Path(directory)
        
        for pattern in file_patterns:
            for file_path in directory_path.rglob(pattern):
                if file_path.is_file():
                    # 检查排除模式
                    should_exclude = False
                    for exclude_pattern in exclude_patterns:
                        if file_path.match(exclude_pattern):
                            should_exclude = True
                            break
                    
                    if not should_exclude:
                        file_paths.append(str(file_path))
        
        logger.info(f"Found {len(file_paths)} files to scan in {directory}")
        
        # 批量扫描文件
        async for match in self.scan_files_batch(file_paths, progress_callback):
            yield match
    
    async def scan_text_chunks(self, text_chunks: List[Dict],
                             progress_callback: Callable = None) -> AsyncGenerator[Dict, None]:
        """批量扫描文本块"""
        async def scan_single_chunk(chunk: Dict) -> List[Dict]:
            try:
                content = chunk.get('content', '')
                source = chunk.get('source', 'unknown')
                
                # 在线程池中执行模式匹配
                loop = asyncio.get_event_loop()
                matches = await loop.run_in_executor(
                    self.executor,
                    pattern_matcher.find_api_keys,
                    content
                )
                
                for match in matches:
                    match['source'] = source
                    match['scan_method'] = 'text_batch'
                    # 添加额外的元数据
                    for key, value in chunk.items():
                        if key not in ['content', 'source']:
                            match[f'chunk_{key}'] = value
                
                self.stats['total_scanned'] += 1
                self.stats['total_matches'] += len(matches)
                
                if progress_callback:
                    await progress_callback(source, len(matches), None)
                
                return matches
                
            except Exception as e:
                self.stats['total_errors'] += 1
                logger.error(f"Error scanning chunk {chunk.get('source', 'unknown')}: {e}")
                if progress_callback:
                    await progress_callback(chunk.get('source', 'unknown'), 0, str(e))
                return []
        
        # 分批处理
        batch_size = self.max_workers * 2  # CPU密集型任务使用更小的批次
        for i in range(0, len(text_chunks), batch_size):
            batch = text_chunks[i:i + batch_size]
            tasks = [scan_single_chunk(chunk) for chunk in batch]
            
            for coro in asyncio.as_completed(tasks):
                matches = await coro
                for match in matches:
                    yield match
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        duration = self.stats['end_time'] - self.stats['start_time']
        
        return {
            'total_scanned': self.stats['total_scanned'],
            'total_matches': self.stats['total_matches'],
            'total_errors': self.stats['total_errors'],
            'duration_seconds': duration,
            'scan_rate': self.stats['total_scanned'] / duration if duration > 0 else 0,
            'match_rate': self.stats['total_matches'] / self.stats['total_scanned'] 
                         if self.stats['total_scanned'] > 0 else 0,
            'error_rate': self.stats['total_errors'] / self.stats['total_scanned'] 
                         if self.stats['total_scanned'] > 0 else 0
        }
    
    async def optimize_performance(self):
        """性能优化"""
        # 预热模式匹配器
        test_content = "AIzaSyDaGmWKa4JsXZ-HjGw7ISLan_Tos80ksOE sk-1234567890abcdef"
        pattern_matcher.find_api_keys(test_content)
        
        # 预热反检测模块
        anti_detection.get_random_user_agent()
        
        logger.info("Performance optimization completed")


# 全局批量扫描器实例
batch_scanner = BatchScanner()
