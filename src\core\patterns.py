"""
API Key模式匹配模块 - 定义各种API Key的正则表达式模式
"""
import re
import math
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class APIKeyPattern:
    """API Key模式定义"""
    
    def __init__(self, name: str, pattern: str, context_keywords: List[str] = None, 
                 entropy_threshold: float = 4.0, min_length: int = 20):
        self.name = name
        self.pattern = re.compile(pattern, re.IGNORECASE)
        self.context_keywords = context_keywords or []
        self.entropy_threshold = entropy_threshold
        self.min_length = min_length
    
    def match(self, text: str) -> List[Dict]:
        """匹配文本中的API Key"""
        matches = []
        for match in self.pattern.finditer(text):
            key = match.group(0)
            if len(key) >= self.min_length and self._calculate_entropy(key) >= self.entropy_threshold:
                matches.append({
                    'key': key,
                    'start': match.start(),
                    'end': match.end(),
                    'type': self.name,
                    'entropy': self._calculate_entropy(key),
                    'context': self._extract_context(text, match.start(), match.end())
                })
        return matches
    
    def _calculate_entropy(self, text: str) -> float:
        """计算字符串的熵值"""
        if not text:
            return 0
        
        # 计算字符频率
        char_counts = {}
        for char in text:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # 计算熵
        entropy = 0
        text_len = len(text)
        for count in char_counts.values():
            probability = count / text_len
            entropy -= probability * math.log2(probability)
        
        return entropy
    
    def _extract_context(self, text: str, start: int, end: int, context_size: int = 100) -> str:
        """提取匹配位置周围的上下文"""
        context_start = max(0, start - context_size)
        context_end = min(len(text), end + context_size)
        return text[context_start:context_end]


class PatternMatcher:
    """模式匹配器"""
    
    def __init__(self):
        self.patterns = self._initialize_patterns()
    
    def _initialize_patterns(self) -> List[APIKeyPattern]:
        """初始化API Key模式"""
        patterns = [
            # Gemini API Key
            APIKeyPattern(
                name="Gemini API Key",
                pattern=r"AIza[0-9A-Za-z\-_]{35}",
                context_keywords=["gemini", "google", "ai", "api_key", "GOOGLE_API_KEY"],
                entropy_threshold=4.0,
                min_length=39
            ),
            
            # OpenRouter API Key
            APIKeyPattern(
                name="OpenRouter API Key",
                pattern=r"sk-or-v1-[a-zA-Z0-9\-_]{40,}",
                context_keywords=["OPENROUTER_API_KEY="],
                entropy_threshold=4.0,
                min_length=48
            ),
            
            # OpenAI API Key
            APIKeyPattern(
                name="OpenAI API Key",
                pattern=r"sk-[a-zA-Z0-9]{48}",
                context_keywords=["openai", "gpt", "api_key", "OPENAI_API_KEY"],
                entropy_threshold=4.5,
                min_length=51
            ),
            
            # Anthropic Claude API Key
            APIKeyPattern(
                name="Anthropic API Key",
                pattern=r"sk-ant-api03-[a-zA-Z0-9\-_]{95}",
                context_keywords=["anthropic", "claude", "ANTHROPIC_API_KEY"],
                entropy_threshold=4.5,
                min_length=108
            ),
            
            # Cohere API Key
            APIKeyPattern(
                name="Cohere API Key",
                pattern=r"[a-zA-Z0-9]{40}",
                context_keywords=["cohere", "COHERE_API_KEY", "co.api"],
                entropy_threshold=4.8,
                min_length=40
            ),
            
            # Hugging Face Token
            APIKeyPattern(
                name="Hugging Face Token",
                pattern=r"hf_[a-zA-Z0-9]{37}",
                context_keywords=["huggingface", "hf_", "HF_TOKEN", "transformers"],
                entropy_threshold=4.5,
                min_length=40
            ),
            
            # Replicate API Token
            APIKeyPattern(
                name="Replicate API Token",
                pattern=r"r8_[a-zA-Z0-9]{37}",
                context_keywords=["replicate", "r8_", "REPLICATE_API_TOKEN"],
                entropy_threshold=4.5,
                min_length=40
            ),
            
            # GitHub Token
            APIKeyPattern(
                name="GitHub Token",
                pattern=r"gh[pousr]_[A-Za-z0-9_]{36,}",
                context_keywords=["github", "gh_", "GITHUB_TOKEN", "personal access token"],
                entropy_threshold=4.5,
                min_length=40
            ),
            
            # AWS Access Key
            APIKeyPattern(
                name="AWS Access Key",
                pattern=r"AKIA[0-9A-Z]{16}",
                context_keywords=["aws", "amazon", "AKIA", "AWS_ACCESS_KEY"],
                entropy_threshold=3.5,
                min_length=20
            ),
            
            # Discord Bot Token
            APIKeyPattern(
                name="Discord Bot Token",
                pattern=r"[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}",
                context_keywords=["discord", "bot", "token", "DISCORD_TOKEN"],
                entropy_threshold=4.5,
                min_length=59
            ),
            
            # Telegram Bot Token
            APIKeyPattern(
                name="Telegram Bot Token",
                pattern=r"\d{8,10}:[a-zA-Z0-9_-]{35}",
                context_keywords=["telegram", "bot", "token", "TELEGRAM_TOKEN"],
                entropy_threshold=4.0,
                min_length=45
            ),

            # Azure API Key (更严格的模式，需要上下文关键词)
            APIKeyPattern(
                name="Azure API Key",
                pattern=r"[a-f0-9]{32}",
                context_keywords=["azure", "microsoft", "AZURE_API_KEY", "cognitive"],
                entropy_threshold=4.5,
                min_length=32
            ),

            # Google Cloud API Key
            APIKeyPattern(
                name="Google Cloud API Key",
                pattern=r"AIza[0-9A-Za-z\-_]{35}",
                context_keywords=["google", "cloud", "gcp", "GOOGLE_CLOUD_API_KEY"],
                entropy_threshold=4.0,
                min_length=39
            ),

            # Stripe API Key
            APIKeyPattern(
                name="Stripe API Key",
                pattern=r"sk_live_[0-9a-zA-Z]{24,}",
                context_keywords=["stripe", "payment", "STRIPE_API_KEY"],
                entropy_threshold=4.5,
                min_length=32
            ),

            # Stripe Test Key
            APIKeyPattern(
                name="Stripe Test Key",
                pattern=r"sk_test_[0-9a-zA-Z]{24,}",
                context_keywords=["stripe", "test", "STRIPE_TEST_KEY"],
                entropy_threshold=4.5,
                min_length=32
            ),

            # SendGrid API Key
            APIKeyPattern(
                name="SendGrid API Key",
                pattern=r"SG\.[a-zA-Z0-9\-_]{22}\.[a-zA-Z0-9\-_]{43}",
                context_keywords=["sendgrid", "email", "SENDGRID_API_KEY"],
                entropy_threshold=4.5,
                min_length=69
            ),

            # Twilio API Key
            APIKeyPattern(
                name="Twilio API Key",
                pattern=r"SK[a-f0-9]{32}",
                context_keywords=["twilio", "sms", "TWILIO_API_KEY"],
                entropy_threshold=4.0,
                min_length=34
            ),

            # Slack Bot Token
            APIKeyPattern(
                name="Slack Bot Token",
                pattern=r"xoxb-[0-9]{11,}-[0-9]{11,}-[a-zA-Z0-9]{24}",
                context_keywords=["slack", "bot", "SLACK_BOT_TOKEN"],
                entropy_threshold=4.5,
                min_length=50
            ),

            # Slack User Token
            APIKeyPattern(
                name="Slack User Token",
                pattern=r"xoxp-[0-9]{11,}-[0-9]{11,}-[0-9]{11,}-[a-f0-9]{32}",
                context_keywords=["slack", "user", "SLACK_USER_TOKEN"],
                entropy_threshold=4.5,
                min_length=70
            ),

            # MongoDB Connection String
            APIKeyPattern(
                name="MongoDB Connection String",
                pattern=r"mongodb(\+srv)?://[a-zA-Z0-9\-_]+:[a-zA-Z0-9\-_]+@[a-zA-Z0-9\-_.]+",
                context_keywords=["mongodb", "mongo", "database", "MONGODB_URI"],
                entropy_threshold=3.5,
                min_length=30
            ),

            # Redis URL
            APIKeyPattern(
                name="Redis URL",
                pattern=r"redis://[a-zA-Z0-9\-_]*:[a-zA-Z0-9\-_]+@[a-zA-Z0-9\-_.]+",
                context_keywords=["redis", "cache", "REDIS_URL"],
                entropy_threshold=3.5,
                min_length=20
            ),

            # JWT Token
            APIKeyPattern(
                name="JWT Token",
                pattern=r"eyJ[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+",
                context_keywords=["jwt", "token", "bearer", "authorization"],
                entropy_threshold=4.5,
                min_length=50
            ),

            # Firebase API Key
            APIKeyPattern(
                name="Firebase API Key",
                pattern=r"AIza[0-9A-Za-z\-_]{35}",
                context_keywords=["firebase", "google", "FIREBASE_API_KEY"],
                entropy_threshold=4.0,
                min_length=39
            ),

            # Mailgun API Key
            APIKeyPattern(
                name="Mailgun API Key",
                pattern=r"key-[a-f0-9]{32}",
                context_keywords=["mailgun", "email", "MAILGUN_API_KEY"],
                entropy_threshold=4.0,
                min_length=36
            ),

            # Cloudinary API Key
            APIKeyPattern(
                name="Cloudinary API Key",
                pattern=r"[0-9]{15}",
                context_keywords=["cloudinary", "image", "CLOUDINARY_API_KEY"],
                entropy_threshold=3.0,
                min_length=15
            ),

            # Pusher API Key
            APIKeyPattern(
                name="Pusher API Key",
                pattern=r"[a-f0-9]{20}",
                context_keywords=["pusher", "websocket", "PUSHER_KEY"],
                entropy_threshold=4.0,
                min_length=20
            )
        ]
        
        return patterns
    
    def find_api_keys(self, text: str, filter_types: List[str] = None) -> List[Dict]:
        """在文本中查找API Key"""
        all_matches = []

        # 按优先级排序模式，更具体的模式优先
        sorted_patterns = sorted(self.patterns, key=lambda p: p.min_length, reverse=True)

        for pattern in sorted_patterns:
            if filter_types and pattern.name not in filter_types:
                continue

            matches = pattern.match(text)
            for match in matches:
                # 检查是否已被更具体的模式匹配
                if self._is_already_matched(match, all_matches):
                    continue

                # 检查上下文关键词
                context_score = self._calculate_context_score(
                    match['context'], pattern.context_keywords
                )
                match['context_score'] = context_score
                all_matches.append(match)

        # 去重和排序
        unique_matches = self._deduplicate_matches(all_matches)
        return sorted(unique_matches, key=lambda x: x['entropy'], reverse=True)
    
    def _calculate_context_score(self, context: str, keywords: List[str]) -> float:
        """计算上下文关键词匹配分数"""
        if not keywords:
            return 0.0
        
        context_lower = context.lower()
        matched_keywords = sum(1 for keyword in keywords if keyword.lower() in context_lower)
        return matched_keywords / len(keywords)
    
    def _is_already_matched(self, new_match: Dict, existing_matches: List[Dict]) -> bool:
        """检查是否已被匹配"""
        new_start = new_match['start']
        new_end = new_match['end']

        for existing in existing_matches:
            existing_start = existing['start']
            existing_end = existing['end']

            # 检查是否有重叠
            if (new_start >= existing_start and new_start < existing_end) or \
               (new_end > existing_start and new_end <= existing_end) or \
               (new_start <= existing_start and new_end >= existing_end):
                return True

        return False

    def _deduplicate_matches(self, matches: List[Dict]) -> List[Dict]:
        """去重匹配结果"""
        seen_keys = set()
        unique_matches = []

        for match in matches:
            key = match['key']
            if key not in seen_keys:
                seen_keys.add(key)
                unique_matches.append(match)

        return unique_matches
    
    def add_custom_pattern(self, pattern: APIKeyPattern):
        """添加自定义模式"""
        self.patterns.append(pattern)
        logger.info(f"Added custom pattern: {pattern.name}")
    
    def get_pattern_names(self) -> List[str]:
        """获取所有模式名称"""
        return [pattern.name for pattern in self.patterns]
    
    def validate_key_format(self, key: str, key_type: str) -> bool:
        """验证API Key格式是否正确"""
        for pattern in self.patterns:
            if pattern.name == key_type:
                matches = pattern.match(key)
                return len(matches) > 0
        return False


# 全局模式匹配器实例
pattern_matcher = PatternMatcher()
