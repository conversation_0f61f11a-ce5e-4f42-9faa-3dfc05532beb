"""
代理管理器 - 管理代理池，支持轮换和健康检查
"""
import asyncio
import random
import time
from typing import List, Dict, Optional, Tuple
import aiohttp
import logging

logger = logging.getLogger(__name__)


class ProxyManager:
    """代理管理器"""
    
    def __init__(self):
        self.proxies: List[Dict] = []
        self.current_index = 0
        self.failed_proxies = set()
        self.last_check_time = {}
        self.check_interval = 300  # 5分钟检查一次
        
    def add_proxy(self, proxy_url: str, proxy_type: str = "http", 
                  username: str = None, password: str = None):
        """添加代理到池中"""
        proxy_info = {
            "url": proxy_url,
            "type": proxy_type,
            "username": username,
            "password": password,
            "success_count": 0,
            "fail_count": 0,
            "last_used": 0,
            "response_time": 0
        }
        self.proxies.append(proxy_info)
        logger.info(f"Added proxy: {proxy_url}")
    
    def load_proxies_from_file(self, file_path: str):
        """从文件加载代理列表"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 支持格式: http://ip:port 或 ip:port
                        if '://' not in line:
                            line = f"http://{line}"
                        self.add_proxy(line)
        except FileNotFoundError:
            logger.warning(f"Proxy file not found: {file_path}")
        except Exception as e:
            logger.error(f"Error loading proxies: {e}")
    
    def get_next_proxy(self) -> Optional[Dict]:
        """获取下一个可用代理"""
        if not self.proxies:
            return None
            
        # 过滤掉失败的代理
        available_proxies = [p for i, p in enumerate(self.proxies) 
                           if i not in self.failed_proxies]
        
        if not available_proxies:
            # 如果所有代理都失败了，重置失败列表
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        # 选择响应时间最快的代理
        available_proxies.sort(key=lambda x: x.get('response_time', 999))
        
        # 随机选择前3个最快的代理之一
        top_proxies = available_proxies[:min(3, len(available_proxies))]
        selected_proxy = random.choice(top_proxies)
        
        return selected_proxy
    
    async def test_proxy(self, proxy_info: Dict, test_url: str = "http://httpbin.org/ip") -> bool:
        """测试代理是否可用"""
        proxy_url = proxy_info["url"]
        start_time = time.time()
        
        try:
            connector = aiohttp.TCPConnector(limit=10)
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                async with session.get(
                    test_url,
                    proxy=proxy_url,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        proxy_info["response_time"] = response_time
                        proxy_info["success_count"] += 1
                        proxy_info["last_used"] = time.time()
                        
                        # 从失败列表中移除
                        proxy_index = self.proxies.index(proxy_info)
                        self.failed_proxies.discard(proxy_index)
                        
                        logger.debug(f"Proxy {proxy_url} is working, response time: {response_time:.2f}s")
                        return True
                    else:
                        raise aiohttp.ClientError(f"HTTP {response.status}")
                        
        except Exception as e:
            proxy_info["fail_count"] += 1
            proxy_index = self.proxies.index(proxy_info)
            self.failed_proxies.add(proxy_index)
            logger.warning(f"Proxy {proxy_url} failed: {e}")
            return False
    
    async def health_check(self):
        """健康检查所有代理"""
        logger.info("Starting proxy health check...")
        
        tasks = []
        for proxy_info in self.proxies:
            # 检查是否需要重新测试
            last_check = self.last_check_time.get(proxy_info["url"], 0)
            if time.time() - last_check > self.check_interval:
                tasks.append(self.test_proxy(proxy_info))
                self.last_check_time[proxy_info["url"]] = time.time()
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            working_count = sum(1 for r in results if r is True)
            logger.info(f"Health check completed: {working_count}/{len(tasks)} proxies working")
    
    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        total = len(self.proxies)
        failed = len(self.failed_proxies)
        working = total - failed
        
        return {
            "total": total,
            "working": working,
            "failed": failed,
            "success_rate": working / total if total > 0 else 0
        }
    
    def format_proxy_for_requests(self, proxy_info: Dict) -> Dict:
        """格式化代理信息供requests使用"""
        if not proxy_info:
            return {}
            
        proxy_url = proxy_info["url"]
        return {
            "http": proxy_url,
            "https": proxy_url
        }
    
    def format_proxy_for_playwright(self, proxy_info: Dict) -> Dict:
        """格式化代理信息供Playwright使用"""
        if not proxy_info:
            return {}
            
        # 解析代理URL
        import urllib.parse
        parsed = urllib.parse.urlparse(proxy_info["url"])
        
        proxy_config = {
            "server": f"{parsed.scheme}://{parsed.hostname}:{parsed.port}"
        }
        
        if parsed.username and parsed.password:
            proxy_config["username"] = parsed.username
            proxy_config["password"] = parsed.password
        elif proxy_info.get("username") and proxy_info.get("password"):
            proxy_config["username"] = proxy_info["username"]
            proxy_config["password"] = proxy_info["password"]
            
        return proxy_config


# 全局代理管理器实例
proxy_manager = ProxyManager()
