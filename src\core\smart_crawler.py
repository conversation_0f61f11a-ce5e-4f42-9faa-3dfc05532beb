"""
智能爬取引擎 - API优先，Playwright备选的统一爬取接口
"""
import asyncio
import aiohttp
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from typing import Dict, List, Optional, Union, Any
import logging
from urllib.parse import urljoin, urlparse

from .proxy_manager import proxy_manager
from .anti_detection import anti_detection

logger = logging.getLogger(__name__)


class CrawlResult:
    """爬取结果"""
    
    def __init__(self, url: str, content: str = "", status_code: int = 0, 
                 headers: Dict = None, method: str = "unknown", error: str = None):
        self.url = url
        self.content = content
        self.status_code = status_code
        self.headers = headers or {}
        self.method = method  # "api", "playwright", "requests"
        self.error = error
        self.success = status_code == 200 and not error


class SmartCrawler:
    """智能爬取引擎"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.playwright = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化爬取引擎"""
        # 初始化aiohttp会话
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=10)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=anti_detection.get_common_headers()
        )
        
        logger.info("SmartCrawler initialized")
    
    async def close(self):
        """关闭爬取引擎"""
        if self.session:
            await self.session.close()
        
        if self.context:
            await self.context.close()
        
        if self.browser:
            await self.browser.close()
        
        if self.playwright:
            await self.playwright.stop()
        
        logger.info("SmartCrawler closed")
    
    async def crawl(self, url: str, method: str = "auto", **kwargs) -> CrawlResult:
        """
        智能爬取URL
        
        Args:
            url: 目标URL
            method: 爬取方法 ("auto", "api", "playwright", "requests")
            **kwargs: 额外参数
        """
        if method == "auto":
            method = self._choose_best_method(url)
        
        # 域名延迟
        domain = urlparse(url).netloc
        await anti_detection.smart_delay(domain)
        
        try:
            if method == "playwright":
                return await self._crawl_with_playwright(url, **kwargs)
            else:
                return await self._crawl_with_requests(url, **kwargs)
        except Exception as e:
            logger.error(f"Crawl failed for {url}: {e}")
            # 如果主要方法失败，尝试备选方法
            if method != "playwright":
                logger.info(f"Trying fallback method for {url}")
                try:
                    return await self._crawl_with_playwright(url, **kwargs)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")
            
            return CrawlResult(url, error=str(e))
    
    def _choose_best_method(self, url: str) -> str:
        """根据URL选择最佳爬取方法"""
        domain = urlparse(url).netloc.lower()
        
        # 需要JavaScript渲染的网站使用Playwright
        js_required_domains = [
            'google.com', 'github.com', 'gitlab.com',
            'stackoverflow.com', 'reddit.com'
        ]
        
        for js_domain in js_required_domains:
            if js_domain in domain:
                return "playwright"
        
        # 其他网站优先使用requests
        return "requests"
    
    async def _crawl_with_requests(self, url: str, **kwargs) -> CrawlResult:
        """使用requests进行爬取"""
        proxy_info = proxy_manager.get_next_proxy()
        proxy = proxy_manager.format_proxy_for_requests(proxy_info) if proxy_info else None
        
        headers = kwargs.get('headers', anti_detection.get_common_headers())
        
        try:
            async with self.session.get(
                url,
                proxy=proxy.get('http') if proxy else None,
                headers=headers,
                allow_redirects=True
            ) as response:
                content = await response.text()
                
                return CrawlResult(
                    url=str(response.url),
                    content=content,
                    status_code=response.status,
                    headers=dict(response.headers),
                    method="requests"
                )
                
        except Exception as e:
            logger.warning(f"Requests crawl failed for {url}: {e}")
            raise
    
    async def _crawl_with_playwright(self, url: str, **kwargs) -> CrawlResult:
        """使用Playwright进行爬取"""
        if not self.playwright:
            await self._initialize_playwright()
        
        page = None
        try:
            page = await self.context.new_page()
            
            # 设置额外的反检测措施
            await self._setup_page_stealth(page)
            
            # 导航到页面
            response = await page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            # 等待页面加载完成
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            # 获取页面内容
            content = await page.content()
            
            return CrawlResult(
                url=page.url,
                content=content,
                status_code=response.status if response else 200,
                headers=dict(response.headers) if response else {},
                method="playwright"
            )
            
        except Exception as e:
            logger.warning(f"Playwright crawl failed for {url}: {e}")
            raise
        finally:
            if page:
                await page.close()
    
    async def _initialize_playwright(self):
        """初始化Playwright"""
        self.playwright = await async_playwright().start()
        
        # 获取代理配置
        proxy_info = proxy_manager.get_next_proxy()
        proxy_config = proxy_manager.format_proxy_for_playwright(proxy_info) if proxy_info else None
        
        # 启动浏览器
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps'
            ]
        )
        
        # 创建浏览器上下文
        context_options = anti_detection.get_playwright_context_options(proxy_config)
        self.context = await self.browser.new_context(**context_options)
        
        logger.info("Playwright initialized")
    
    async def _setup_page_stealth(self, page: Page):
        """设置页面隐身模式"""
        # 注入反检测脚本
        await page.add_init_script("""
            // 移除webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪装Chrome运行时
            window.chrome = {
                runtime: {},
            };
            
            // 伪装权限API
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 伪装插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪装语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        """)
        
        # 设置额外的请求头
        await page.set_extra_http_headers({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
    
    async def crawl_multiple(self, urls: List[str], max_concurrent: int = 5) -> List[CrawlResult]:
        """并发爬取多个URL"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url):
            async with semaphore:
                return await self.crawl(url)
        
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(CrawlResult(urls[i], error=str(result)))
            else:
                processed_results.append(result)
        
        return processed_results


# 全局爬取引擎实例
smart_crawler = SmartCrawler()
