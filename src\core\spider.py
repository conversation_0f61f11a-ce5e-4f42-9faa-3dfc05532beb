"""
主爬虫类 - 统一的API Key爬取接口
"""
import asyncio
import time
from typing import List, Dict, Optional, AsyncGenerator, Union
from pathlib import Path
import logging

from .smart_crawler import SmartCrawler
from .patterns import pattern_matcher
from .validator import APIKeyValidator
from .batch_scanner import BatchScanner
from ..engines.github_scanner import GitHubScanner
from ..engines.search_engine import SearchEngineManager

logger = logging.getLogger(__name__)


class APIKeySpider:
    """主API Key爬虫类"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.results = []
        self.stats = {
            'total_sources': 0,
            'total_matches': 0,
            'total_errors': 0,
            'start_time': 0,
            'end_time': 0,
            'sources_by_type': {}
        }
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.stats['start_time'] = time.time()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        self.stats['end_time'] = time.time()
    
    async def crawl_urls(self, urls: List[str], 
                        validate_keys: bool = False) -> List[Dict]:
        """爬取URL列表"""
        logger.info(f"Starting URL crawl for {len(urls)} URLs")
        
        matches = []
        
        async with SmartCrawler() as crawler:
            for url in urls:
                try:
                    result = await crawler.crawl(url)
                    
                    if result.success:
                        url_matches = pattern_matcher.find_api_keys(result.content)
                        
                        for match in url_matches:
                            match.update({
                                'source': url,
                                'source_type': 'url',
                                'page_title': self._extract_title(result.content),
                                'crawl_method': result.method
                            })
                            
                            # 验证API Key
                            if validate_keys:
                                match['validation'] = await self._validate_key(match)
                            
                            matches.append(match)
                        
                        self.stats['total_sources'] += 1
                        self.stats['total_matches'] += len(url_matches)
                        
                    else:
                        self.stats['total_errors'] += 1
                        logger.warning(f"Failed to crawl {url}: {result.error}")
                        
                except Exception as e:
                    self.stats['total_errors'] += 1
                    logger.error(f"Error crawling {url}: {e}")
        
        self._update_source_stats('url', len(urls))
        return matches
    
    async def scan_files(self, file_paths: List[str], 
                        validate_keys: bool = False) -> List[Dict]:
        """扫描文件列表"""
        logger.info(f"Starting file scan for {len(file_paths)} files")
        
        matches = []
        
        async with BatchScanner() as scanner:
            async for match in scanner.scan_files_batch(file_paths):
                match.update({
                    'source_type': 'file',
                    'file_size': self._get_file_size(match['source'])
                })
                
                # 验证API Key
                if validate_keys:
                    match['validation'] = await self._validate_key(match)
                
                matches.append(match)
        
        self._update_source_stats('file', len(file_paths))
        return matches
    
    async def scan_directory(self, directory: str, 
                           file_patterns: List[str] = None,
                           validate_keys: bool = False) -> List[Dict]:
        """扫描目录"""
        logger.info(f"Starting directory scan for {directory}")
        
        matches = []
        
        async with BatchScanner() as scanner:
            async for match in scanner.scan_directory_recursive(directory, file_patterns):
                match.update({
                    'source_type': 'directory',
                    'directory': directory
                })
                
                # 验证API Key
                if validate_keys:
                    match['validation'] = await self._validate_key(match)
                
                matches.append(match)
        
        self._update_source_stats('directory', 1)
        return matches
    
    async def search_github(self, key_types: List[str] = None,
                          max_results: int = 100,
                          token: str = None,
                          validate_keys: bool = False) -> List[Dict]:
        """GitHub搜索"""
        logger.info(f"Starting GitHub search for {key_types or 'all'} key types")
        
        matches = []
        
        async with GitHubScanner(token) as scanner:
            async for match in scanner.search_api_keys(key_types, max_results):
                match.update({
                    'source_type': 'github_search'
                })
                
                # 验证API Key
                if validate_keys:
                    match['validation'] = await self._validate_key(match)
                
                matches.append(match)
        
        self._update_source_stats('github_search', 1)
        return matches
    
    async def search_engines(self, key_types: List[str] = None,
                           engines: List[str] = None,
                           max_results: int = 20,
                           validate_keys: bool = False) -> List[Dict]:
        """搜索引擎搜索"""
        logger.info(f"Starting search engine search with {engines or ['google']}")
        
        matches = []
        
        async with SearchEngineManager() as manager:
            async for match in manager.search_api_keys(key_types, max_results, engines):
                match.update({
                    'source_type': 'search_engine'
                })
                
                # 验证API Key
                if validate_keys:
                    match['validation'] = await self._validate_key(match)
                
                matches.append(match)
        
        self._update_source_stats('search_engine', 1)
        return matches
    
    async def comprehensive_scan(self, targets: Dict,
                               validate_keys: bool = False) -> List[Dict]:
        """综合扫描"""
        logger.info("Starting comprehensive scan")
        
        all_matches = []
        
        # URL扫描
        if 'urls' in targets:
            url_matches = await self.crawl_urls(targets['urls'], validate_keys)
            all_matches.extend(url_matches)
        
        # 文件扫描
        if 'files' in targets:
            file_matches = await self.scan_files(targets['files'], validate_keys)
            all_matches.extend(file_matches)
        
        # 目录扫描
        if 'directories' in targets:
            for directory in targets['directories']:
                dir_matches = await self.scan_directory(directory, validate_keys=validate_keys)
                all_matches.extend(dir_matches)
        
        # GitHub搜索
        if 'github' in targets:
            github_config = targets['github']
            github_matches = await self.search_github(
                key_types=github_config.get('key_types'),
                max_results=github_config.get('max_results', 100),
                token=github_config.get('token'),
                validate_keys=validate_keys
            )
            all_matches.extend(github_matches)
        
        # 搜索引擎搜索
        if 'search_engines' in targets:
            search_config = targets['search_engines']
            search_matches = await self.search_engines(
                key_types=search_config.get('key_types'),
                engines=search_config.get('engines'),
                max_results=search_config.get('max_results', 20),
                validate_keys=validate_keys
            )
            all_matches.extend(search_matches)
        
        # 去重
        unique_matches = self._deduplicate_matches(all_matches)
        
        self.results = unique_matches
        return unique_matches
    
    async def _validate_key(self, match: Dict) -> Dict:
        """验证API Key"""
        try:
            async with APIKeyValidator() as validator:
                return await validator.validate_key(match['key'], match['type'])
        except Exception as e:
            logger.error(f"Validation error for {match['type']}: {e}")
            return {
                'valid': None,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    def _extract_title(self, html_content: str) -> str:
        """提取页面标题"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            title_elem = soup.find('title')
            return title_elem.get_text(strip=True) if title_elem else "Unknown"
        except:
            return "Unknown"
    
    def _get_file_size(self, file_path: str) -> int:
        """获取文件大小"""
        try:
            return Path(file_path).stat().st_size
        except:
            return 0
    
    def _deduplicate_matches(self, matches: List[Dict]) -> List[Dict]:
        """去重匹配结果"""
        seen_keys = set()
        unique_matches = []
        
        for match in matches:
            key = match['key']
            if key not in seen_keys:
                seen_keys.add(key)
                unique_matches.append(match)
        
        return unique_matches
    
    def _update_source_stats(self, source_type: str, count: int):
        """更新来源统计"""
        if source_type not in self.stats['sources_by_type']:
            self.stats['sources_by_type'][source_type] = 0
        self.stats['sources_by_type'][source_type] += count
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time']
        
        return {
            'total_sources': self.stats['total_sources'],
            'total_matches': self.stats['total_matches'],
            'total_errors': self.stats['total_errors'],
            'duration_seconds': duration,
            'sources_by_type': self.stats['sources_by_type'],
            'unique_keys': len(set(match['key'] for match in self.results)),
            'key_types_found': len(set(match['type'] for match in self.results)),
            'success_rate': (self.stats['total_sources'] / 
                           (self.stats['total_sources'] + self.stats['total_errors'])) 
                           if (self.stats['total_sources'] + self.stats['total_errors']) > 0 else 0
        }
    
    def export_results(self, format: str = 'json') -> str:
        """导出结果"""
        if format.lower() == 'json':
            import json
            return json.dumps(self.results, indent=2, ensure_ascii=False)
        elif format.lower() == 'csv':
            import csv
            import io
            
            output = io.StringIO()
            if self.results:
                fieldnames = self.results[0].keys()
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.results)
            
            return output.getvalue()
        else:
            raise ValueError(f"Unsupported format: {format}")


# 全局爬虫实例
api_key_spider = APIKeySpider()
