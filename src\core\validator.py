"""
API Key验证器 - 验证API Key的有效性和权限
"""
import asyncio
import aiohttp
import json
from typing import Dict, Optional, List, Tuple
import logging
from urllib.parse import urljoin

from .anti_detection import anti_detection

logger = logging.getLogger(__name__)


class APIKeyValidator:
    """API Key验证器"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.validation_endpoints = self._load_validation_endpoints()
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化验证器"""
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=30)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=anti_detection.get_common_headers()
        )
        
        logger.info("API Key validator initialized")
    
    async def close(self):
        """关闭验证器"""
        if self.session:
            await self.session.close()
    
    async def validate_key(self, api_key: str, key_type: str) -> Dict:
        """验证API Key"""
        validation_info = self.validation_endpoints.get(key_type)
        if not validation_info:
            return {
                'valid': None,
                'error': f'No validation endpoint for {key_type}',
                'permissions': [],
                'rate_limit': None
            }
        
        try:
            # 根据API类型选择验证方法
            if key_type == "OpenAI API Key":
                return await self._validate_openai_key(api_key)
            elif key_type == "GitHub Token":
                return await self._validate_github_token(api_key)
            elif key_type == "Anthropic API Key":
                return await self._validate_anthropic_key(api_key)
            elif key_type == "Gemini API Key":
                return await self._validate_gemini_key(api_key)
            else:
                return await self._validate_generic_key(api_key, validation_info)
                
        except Exception as e:
            logger.error(f"Error validating {key_type}: {e}")
            return {
                'valid': False,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    async def _validate_openai_key(self, api_key: str) -> Dict:
        """验证OpenAI API Key"""
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        try:
            # 使用models端点验证
            async with self.session.get(
                'https://api.openai.com/v1/models',
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model['id'] for model in data.get('data', [])]
                    
                    return {
                        'valid': True,
                        'error': None,
                        'permissions': models[:10],  # 限制显示数量
                        'rate_limit': self._extract_rate_limit(response.headers),
                        'organization': response.headers.get('openai-organization')
                    }
                elif response.status == 401:
                    return {
                        'valid': False,
                        'error': 'Invalid API key',
                        'permissions': [],
                        'rate_limit': None
                    }
                else:
                    error_text = await response.text()
                    return {
                        'valid': False,
                        'error': f'HTTP {response.status}: {error_text}',
                        'permissions': [],
                        'rate_limit': None
                    }
                    
        except Exception as e:
            return {
                'valid': False,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    async def _validate_github_token(self, token: str) -> Dict:
        """验证GitHub Token"""
        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        try:
            # 使用user端点验证
            async with self.session.get(
                'https://api.github.com/user',
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 获取权限信息
                    scopes = response.headers.get('X-OAuth-Scopes', '').split(', ')
                    
                    return {
                        'valid': True,
                        'error': None,
                        'permissions': [s for s in scopes if s],
                        'rate_limit': self._extract_github_rate_limit(response.headers),
                        'user': data.get('login'),
                        'user_type': data.get('type')
                    }
                elif response.status == 401:
                    return {
                        'valid': False,
                        'error': 'Invalid token',
                        'permissions': [],
                        'rate_limit': None
                    }
                else:
                    error_text = await response.text()
                    return {
                        'valid': False,
                        'error': f'HTTP {response.status}: {error_text}',
                        'permissions': [],
                        'rate_limit': None
                    }
                    
        except Exception as e:
            return {
                'valid': False,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    async def _validate_anthropic_key(self, api_key: str) -> Dict:
        """验证Anthropic API Key"""
        headers = {
            'x-api-key': api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }
        
        # 使用简单的消息测试
        data = {
            'model': 'claude-3-haiku-20240307',
            'max_tokens': 1,
            'messages': [{'role': 'user', 'content': 'Hi'}]
        }
        
        try:
            async with self.session.post(
                'https://api.anthropic.com/v1/messages',
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    return {
                        'valid': True,
                        'error': None,
                        'permissions': ['claude-3-haiku'],
                        'rate_limit': self._extract_rate_limit(response.headers)
                    }
                elif response.status == 401:
                    return {
                        'valid': False,
                        'error': 'Invalid API key',
                        'permissions': [],
                        'rate_limit': None
                    }
                else:
                    error_text = await response.text()
                    return {
                        'valid': False,
                        'error': f'HTTP {response.status}: {error_text}',
                        'permissions': [],
                        'rate_limit': None
                    }
                    
        except Exception as e:
            return {
                'valid': False,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    async def _validate_gemini_key(self, api_key: str) -> Dict:
        """验证Gemini API Key"""
        try:
            # 使用models端点验证
            url = f'https://generativelanguage.googleapis.com/v1beta/models?key={api_key}'
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model['name'] for model in data.get('models', [])]
                    
                    return {
                        'valid': True,
                        'error': None,
                        'permissions': models[:5],  # 限制显示数量
                        'rate_limit': self._extract_rate_limit(response.headers)
                    }
                elif response.status == 400:
                    return {
                        'valid': False,
                        'error': 'Invalid API key',
                        'permissions': [],
                        'rate_limit': None
                    }
                else:
                    error_text = await response.text()
                    return {
                        'valid': False,
                        'error': f'HTTP {response.status}: {error_text}',
                        'permissions': [],
                        'rate_limit': None
                    }
                    
        except Exception as e:
            return {
                'valid': False,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    async def _validate_generic_key(self, api_key: str, validation_info: Dict) -> Dict:
        """通用API Key验证"""
        endpoint = validation_info.get('endpoint')
        method = validation_info.get('method', 'GET')
        headers = validation_info.get('headers', {})
        
        # 替换API Key占位符
        for key, value in headers.items():
            if isinstance(value, str):
                headers[key] = value.replace('{api_key}', api_key)
        
        try:
            if method.upper() == 'GET':
                async with self.session.get(endpoint, headers=headers) as response:
                    return self._parse_validation_response(response, validation_info)
            elif method.upper() == 'POST':
                data = validation_info.get('data', {})
                async with self.session.post(endpoint, headers=headers, json=data) as response:
                    return self._parse_validation_response(response, validation_info)
            else:
                return {
                    'valid': False,
                    'error': f'Unsupported method: {method}',
                    'permissions': [],
                    'rate_limit': None
                }
                
        except Exception as e:
            return {
                'valid': False,
                'error': str(e),
                'permissions': [],
                'rate_limit': None
            }
    
    def _parse_validation_response(self, response, validation_info: Dict) -> Dict:
        """解析验证响应"""
        success_codes = validation_info.get('success_codes', [200])
        
        if response.status in success_codes:
            return {
                'valid': True,
                'error': None,
                'permissions': ['validated'],
                'rate_limit': self._extract_rate_limit(response.headers)
            }
        else:
            return {
                'valid': False,
                'error': f'HTTP {response.status}',
                'permissions': [],
                'rate_limit': None
            }
    
    def _extract_rate_limit(self, headers) -> Optional[Dict]:
        """提取速率限制信息"""
        rate_limit = {}
        
        # 通用速率限制头
        if 'X-RateLimit-Limit' in headers:
            rate_limit['limit'] = headers['X-RateLimit-Limit']
        if 'X-RateLimit-Remaining' in headers:
            rate_limit['remaining'] = headers['X-RateLimit-Remaining']
        if 'X-RateLimit-Reset' in headers:
            rate_limit['reset'] = headers['X-RateLimit-Reset']
        
        return rate_limit if rate_limit else None
    
    def _extract_github_rate_limit(self, headers) -> Optional[Dict]:
        """提取GitHub速率限制信息"""
        return {
            'limit': headers.get('X-RateLimit-Limit'),
            'remaining': headers.get('X-RateLimit-Remaining'),
            'reset': headers.get('X-RateLimit-Reset'),
            'used': headers.get('X-RateLimit-Used')
        }
    
    def _load_validation_endpoints(self) -> Dict:
        """加载验证端点配置"""
        return {
            "Stripe API Key": {
                "endpoint": "https://api.stripe.com/v1/account",
                "method": "GET",
                "headers": {"Authorization": "Bearer {api_key}"},
                "success_codes": [200]
            },
            "SendGrid API Key": {
                "endpoint": "https://api.sendgrid.com/v3/user/account",
                "method": "GET",
                "headers": {"Authorization": "Bearer {api_key}"},
                "success_codes": [200]
            },
            "Twilio API Key": {
                "endpoint": "https://api.twilio.com/2010-04-01/Accounts.json",
                "method": "GET",
                "headers": {"Authorization": "Basic {api_key}"},
                "success_codes": [200]
            }
        }


# 全局验证器实例
api_key_validator = APIKeyValidator()
