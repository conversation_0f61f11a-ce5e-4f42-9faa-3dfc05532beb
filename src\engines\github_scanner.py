"""
GitHub搜索引擎 - 通过GitHub API和Web搜索发现API Key
"""
import asyncio
import json
import base64
from typing import List, Dict, Optional, AsyncGenerator
import aiohttp
from urllib.parse import quote
import logging

from ..core.smart_crawler import <PERSON>Crawler, CrawlResult
from ..core.patterns import pattern_matcher
from ..core.anti_detection import anti_detection

logger = logging.getLogger(__name__)


class GitHubScanner:
    """GitHub搜索扫描器"""
    
    def __init__(self, token: str = None):
        self.token = token
        self.api_base = "https://api.github.com"
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limit_remaining = 60  # 未认证用户限制
        self.rate_limit_reset = 0
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化GitHub扫描器"""
        headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': anti_detection.get_random_user_agent()
        }
        
        if self.token:
            headers['Authorization'] = f'token {self.token}'
            self.rate_limit_remaining = 5000  # 认证用户限制
        
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=30)
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            connector=connector,
            timeout=timeout
        )
        
        logger.info("GitHub scanner initialized")
    
    async def close(self):
        """关闭扫描器"""
        if self.session:
            await self.session.close()
    
    async def search_code(self, query: str, max_results: int = 100) -> List[Dict]:
        """搜索GitHub代码"""
        if not await self._check_rate_limit():
            logger.warning("GitHub API rate limit exceeded")
            return []
        
        url = f"{self.api_base}/search/code"
        params = {
            'q': query,
            'per_page': min(100, max_results),
            'sort': 'indexed'
        }
        
        try:
            await anti_detection.smart_delay("github.com")
            
            async with self.session.get(url, params=params) as response:
                await self._update_rate_limit(response)
                
                if response.status == 200:
                    data = await response.json()
                    return data.get('items', [])
                elif response.status == 403:
                    logger.warning("GitHub API rate limit exceeded")
                    return []
                else:
                    logger.error(f"GitHub API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"GitHub search failed: {e}")
            return []
    
    async def get_file_content(self, file_info: Dict) -> Optional[str]:
        """获取文件内容"""
        if not await self._check_rate_limit():
            return None
        
        try:
            # 通过API获取文件内容
            url = file_info.get('url')
            if not url:
                return None
            
            await anti_detection.smart_delay("github.com")
            
            async with self.session.get(url) as response:
                await self._update_rate_limit(response)
                
                if response.status == 200:
                    data = await response.json()
                    content = data.get('content', '')
                    
                    # Base64解码
                    if data.get('encoding') == 'base64':
                        try:
                            return base64.b64decode(content).decode('utf-8')
                        except Exception as e:
                            logger.warning(f"Failed to decode file content: {e}")
                            return None
                    else:
                        return content
                else:
                    logger.warning(f"Failed to get file content: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting file content: {e}")
            return None
    
    async def search_api_keys(self, key_types: List[str] = None, 
                            max_results_per_query: int = 50) -> AsyncGenerator[Dict, None]:
        """搜索API Key"""
        # 加载搜索查询
        queries = self._get_search_queries(key_types)
        
        for query_info in queries:
            query = query_info['query']
            key_type = query_info['type']
            
            logger.info(f"Searching GitHub for: {query}")
            
            # 搜索代码
            results = await self.search_code(query, max_results_per_query)
            
            for file_info in results:
                # 获取文件内容
                content = await self.get_file_content(file_info)
                if not content:
                    continue
                
                # 扫描API Key
                matches = pattern_matcher.find_api_keys(content, [key_type] if key_type else None)
                
                for match in matches:
                    match.update({
                        'source': 'github',
                        'repository': file_info.get('repository', {}).get('full_name', 'unknown'),
                        'file_path': file_info.get('path', 'unknown'),
                        'file_url': file_info.get('html_url', ''),
                        'repository_url': file_info.get('repository', {}).get('html_url', ''),
                        'last_modified': file_info.get('repository', {}).get('updated_at', ''),
                        'file_size': file_info.get('size', 0)
                    })
                    
                    yield match
                
                # 避免过快请求
                await asyncio.sleep(0.5)
    
    async def search_repositories(self, query: str, max_results: int = 100) -> List[Dict]:
        """搜索仓库"""
        if not await self._check_rate_limit():
            return []
        
        url = f"{self.api_base}/search/repositories"
        params = {
            'q': query,
            'per_page': min(100, max_results),
            'sort': 'updated'
        }
        
        try:
            await anti_detection.smart_delay("github.com")
            
            async with self.session.get(url, params=params) as response:
                await self._update_rate_limit(response)
                
                if response.status == 200:
                    data = await response.json()
                    return data.get('items', [])
                else:
                    logger.error(f"GitHub repository search error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"GitHub repository search failed: {e}")
            return []
    
    async def scan_repository_files(self, repo_full_name: str, 
                                  file_patterns: List[str] = None) -> AsyncGenerator[Dict, None]:
        """扫描仓库文件"""
        if file_patterns is None:
            file_patterns = ['.env', 'config.py', 'settings.py', '.json', '.yaml', '.yml']
        
        # 获取仓库文件树
        url = f"{self.api_base}/repos/{repo_full_name}/git/trees/main"
        params = {'recursive': '1'}
        
        try:
            await anti_detection.smart_delay("github.com")
            
            async with self.session.get(url, params=params) as response:
                await self._update_rate_limit(response)
                
                if response.status != 200:
                    logger.warning(f"Failed to get repository tree: {response.status}")
                    return
                
                data = await response.json()
                tree = data.get('tree', [])
                
                # 过滤文件
                target_files = []
                for item in tree:
                    if item.get('type') == 'blob':  # 文件
                        path = item.get('path', '')
                        if any(pattern in path for pattern in file_patterns):
                            target_files.append(item)
                
                # 扫描文件
                for file_item in target_files[:50]:  # 限制文件数量
                    file_url = file_item.get('url')
                    if not file_url:
                        continue
                    
                    # 获取文件内容
                    await anti_detection.smart_delay("github.com")
                    
                    async with self.session.get(file_url) as file_response:
                        await self._update_rate_limit(file_response)
                        
                        if file_response.status == 200:
                            file_data = await file_response.json()
                            content = file_data.get('content', '')
                            
                            if file_data.get('encoding') == 'base64':
                                try:
                                    content = base64.b64decode(content).decode('utf-8')
                                except:
                                    continue
                            
                            # 扫描API Key
                            matches = pattern_matcher.find_api_keys(content)
                            
                            for match in matches:
                                match.update({
                                    'source': 'github_repo_scan',
                                    'repository': repo_full_name,
                                    'file_path': file_item.get('path'),
                                    'file_url': f"https://github.com/{repo_full_name}/blob/main/{file_item.get('path')}",
                                    'repository_url': f"https://github.com/{repo_full_name}"
                                })
                                
                                yield match
                    
                    await asyncio.sleep(0.3)  # 避免过快请求
                    
        except Exception as e:
            logger.error(f"Error scanning repository {repo_full_name}: {e}")
    
    def _get_search_queries(self, key_types: List[str] = None) -> List[Dict]:
        """获取搜索查询列表"""
        # 加载搜索查询配置
        try:
            with open('config/search_queries.json', 'r', encoding='utf-8') as f:
                all_queries = json.load(f)
        except:
            all_queries = {}
        
        queries = []
        
        # 根据key_types过滤查询
        if key_types:
            for key_type in key_types:
                if 'gemini' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'Gemini API Key'} 
                                  for q in all_queries.get('gemini_queries', [])])
                elif 'openrouter' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'OpenRouter API Key'} 
                                  for q in all_queries.get('openrouter_queries', [])])
                elif 'openai' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'OpenAI API Key'} 
                                  for q in all_queries.get('openai_queries', [])])
                elif 'github' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'GitHub Token'} 
                                  for q in all_queries.get('github_queries', [])])
        else:
            # 使用所有查询
            for query_type, query_list in all_queries.items():
                if query_type.endswith('_queries'):
                    key_name = query_type.replace('_queries', '').title() + ' API Key'
                    queries.extend([{'query': q, 'type': key_name} for q in query_list])
        
        return queries[:20]  # 限制查询数量
    
    async def _check_rate_limit(self) -> bool:
        """检查API速率限制"""
        if self.rate_limit_remaining <= 1:
            import time
            if time.time() < self.rate_limit_reset:
                return False
        return True
    
    async def _update_rate_limit(self, response: aiohttp.ClientResponse):
        """更新速率限制信息"""
        self.rate_limit_remaining = int(response.headers.get('X-RateLimit-Remaining', 0))
        self.rate_limit_reset = int(response.headers.get('X-RateLimit-Reset', 0))
        
        logger.debug(f"GitHub API rate limit: {self.rate_limit_remaining} remaining")


# 全局GitHub扫描器实例
github_scanner = GitHubScanner()
