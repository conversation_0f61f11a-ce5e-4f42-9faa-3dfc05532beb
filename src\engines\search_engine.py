"""
搜索引擎爬虫 - 通过Google等搜索引擎发现API Key
"""
import asyncio
import json
import re
from typing import List, Dict, Optional, AsyncGenerator
from urllib.parse import quote, urljoin, urlparse
import logging

from ..core.smart_crawler import SmartCrawler, CrawlResult
from ..core.patterns import pattern_matcher
from ..core.anti_detection import anti_detection

logger = logging.getLogger(__name__)


class SearchEngine:
    """搜索引擎爬虫基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.crawler: Optional[SmartCrawler] = None
        
    async def search(self, query: str, max_results: int = 50) -> List[Dict]:
        """搜索查询"""
        raise NotImplementedError
    
    async def extract_links(self, search_results: List[Dict]) -> List[str]:
        """从搜索结果中提取链接"""
        links = []
        for result in search_results:
            link = result.get('url') or result.get('link')
            if link:
                links.append(link)
        return links


class GoogleSearchEngine(SearchEngine):
    """Google搜索引擎"""
    
    def __init__(self):
        super().__init__("Google")
        self.base_url = "https://www.google.com/search"
        
    async def search(self, query: str, max_results: int = 50) -> List[Dict]:
        """Google搜索"""
        if not self.crawler:
            self.crawler = SmartCrawler()
            await self.crawler.initialize()
        
        search_url = f"{self.base_url}?q={quote(query)}&num={min(100, max_results)}"
        
        try:
            # 使用Playwright爬取Google搜索结果
            result = await self.crawler.crawl(search_url, method="playwright")
            
            if result.success:
                return self._parse_google_results(result.content)
            else:
                logger.error(f"Google search failed: {result.error}")
                return []
                
        except Exception as e:
            logger.error(f"Google search error: {e}")
            return []
    
    def _parse_google_results(self, html_content: str) -> List[Dict]:
        """解析Google搜索结果"""
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html_content, 'html.parser')
        results = []
        
        # 查找搜索结果
        search_results = soup.find_all('div', class_='g')
        
        for result_div in search_results:
            try:
                # 提取标题和链接
                title_elem = result_div.find('h3')
                link_elem = result_div.find('a')
                
                if title_elem and link_elem:
                    title = title_elem.get_text(strip=True)
                    url = link_elem.get('href')
                    
                    # 清理URL
                    if url and url.startswith('/url?q='):
                        url = url.split('/url?q=')[1].split('&')[0]
                    
                    # 提取描述
                    desc_elem = result_div.find('span', class_='st') or result_div.find('div', class_='s')
                    description = desc_elem.get_text(strip=True) if desc_elem else ""
                    
                    if url and not url.startswith('http'):
                        continue
                    
                    results.append({
                        'title': title,
                        'url': url,
                        'description': description,
                        'source': 'google'
                    })
                    
            except Exception as e:
                logger.debug(f"Error parsing search result: {e}")
                continue
        
        return results


class PastebinSearchEngine(SearchEngine):
    """Pastebin搜索引擎"""
    
    def __init__(self):
        super().__init__("Pastebin")
        self.base_url = "https://pastebin.com"
        
    async def search(self, query: str, max_results: int = 50) -> List[Dict]:
        """Pastebin搜索"""
        if not self.crawler:
            self.crawler = SmartCrawler()
            await self.crawler.initialize()
        
        # Pastebin没有直接搜索API，通过Google搜索Pastebin内容
        google_query = f"site:pastebin.com {query}"
        google_engine = GoogleSearchEngine()
        google_results = await google_engine.search(google_query, max_results)
        
        # 过滤Pastebin链接
        pastebin_results = []
        for result in google_results:
            if 'pastebin.com' in result.get('url', ''):
                pastebin_results.append(result)
        
        return pastebin_results[:max_results]


class SearchEngineManager:
    """搜索引擎管理器"""
    
    def __init__(self):
        self.engines = {
            'google': GoogleSearchEngine(),
            'pastebin': PastebinSearchEngine()
        }
        self.crawler: Optional[SmartCrawler] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.crawler = SmartCrawler()
        await self.crawler.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.crawler:
            await self.crawler.close()
    
    async def search_api_keys(self, key_types: List[str] = None, 
                            max_results_per_query: int = 20,
                            engines: List[str] = None) -> AsyncGenerator[Dict, None]:
        """搜索API Key"""
        if engines is None:
            engines = ['google']
        
        # 加载搜索查询
        queries = self._get_search_queries(key_types)
        
        for query_info in queries:
            query = query_info['query']
            key_type = query_info['type']
            
            logger.info(f"Searching for: {query}")
            
            for engine_name in engines:
                if engine_name not in self.engines:
                    continue
                
                engine = self.engines[engine_name]
                
                try:
                    # 搜索
                    search_results = await engine.search(query, max_results_per_query)
                    
                    # 提取链接
                    links = await engine.extract_links(search_results)
                    
                    # 爬取页面内容并扫描API Key
                    for link in links[:10]:  # 限制每个查询的链接数量
                        try:
                            # 过滤不相关的链接
                            if not self._is_relevant_link(link):
                                continue
                            
                            # 爬取页面
                            result = await self.crawler.crawl(link)
                            
                            if result.success:
                                # 扫描API Key
                                matches = pattern_matcher.find_api_keys(
                                    result.content, 
                                    [key_type] if key_type else None
                                )
                                
                                for match in matches:
                                    match.update({
                                        'source': f'search_engine_{engine_name}',
                                        'search_query': query,
                                        'page_url': link,
                                        'page_title': self._extract_title(result.content),
                                        'engine': engine_name
                                    })
                                    
                                    yield match
                            
                            # 避免过快请求
                            await asyncio.sleep(2)
                            
                        except Exception as e:
                            logger.debug(f"Error crawling {link}: {e}")
                            continue
                
                except Exception as e:
                    logger.error(f"Search engine {engine_name} error: {e}")
                    continue
                
                # 引擎间延迟
                await asyncio.sleep(3)
    
    def _get_search_queries(self, key_types: List[str] = None) -> List[Dict]:
        """获取搜索查询列表"""
        try:
            with open('config/search_queries.json', 'r', encoding='utf-8') as f:
                all_queries = json.load(f)
        except:
            all_queries = {}
        
        queries = []
        
        if key_types:
            for key_type in key_types:
                if 'gemini' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'Gemini API Key'} 
                                  for q in all_queries.get('gemini_queries', [])])
                elif 'openrouter' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'OpenRouter API Key'} 
                                  for q in all_queries.get('openrouter_queries', [])])
                elif 'openai' in key_type.lower():
                    queries.extend([{'query': q, 'type': 'OpenAI API Key'} 
                                  for q in all_queries.get('openai_queries', [])])
        else:
            # 使用通用查询
            queries.extend([{'query': q, 'type': None} 
                          for q in all_queries.get('general_queries', [])])
        
        return queries[:10]  # 限制查询数量
    
    def _is_relevant_link(self, url: str) -> bool:
        """判断链接是否相关"""
        if not url:
            return False
        
        # 排除不相关的域名
        excluded_domains = [
            'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
            'linkedin.com', 'instagram.com', 'reddit.com', 'stackoverflow.com'
        ]
        
        domain = urlparse(url).netloc.lower()
        for excluded in excluded_domains:
            if excluded in domain:
                return False
        
        # 包含相关的域名
        relevant_domains = [
            'github.com', 'gitlab.com', 'pastebin.com', 'gist.github.com',
            'codepen.io', 'jsfiddle.net', 'repl.it', 'codesandbox.io'
        ]
        
        for relevant in relevant_domains:
            if relevant in domain:
                return True
        
        # 检查URL路径是否包含相关关键词
        relevant_keywords = [
            'config', 'env', 'api', 'key', 'token', 'secret', 'credential'
        ]
        
        url_lower = url.lower()
        for keyword in relevant_keywords:
            if keyword in url_lower:
                return True
        
        return False
    
    def _extract_title(self, html_content: str) -> str:
        """提取页面标题"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            title_elem = soup.find('title')
            return title_elem.get_text(strip=True) if title_elem else "Unknown"
        except:
            return "Unknown"


# 全局搜索引擎管理器实例
search_engine_manager = SearchEngineManager()
