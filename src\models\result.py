"""
结果数据模型
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from datetime import datetime
import json


@dataclass
class APIKeyMatch:
    """API Key匹配结果"""
    key: str
    type: str
    entropy: float
    context: str
    source: str
    start: int
    end: int
    context_score: float = 0.0
    scan_method: str = "unknown"
    source_type: str = "unknown"
    timestamp: datetime = field(default_factory=datetime.now)
    validation: Optional[Dict] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        result = {
            'key': self.key,
            'type': self.type,
            'entropy': self.entropy,
            'context': self.context,
            'source': self.source,
            'start': self.start,
            'end': self.end,
            'context_score': self.context_score,
            'scan_method': self.scan_method,
            'source_type': self.source_type,
            'timestamp': self.timestamp.isoformat(),
            'validation': self.validation,
            'metadata': self.metadata
        }
        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'APIKeyMatch':
        """从字典创建"""
        timestamp = data.get('timestamp')
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)
        elif timestamp is None:
            timestamp = datetime.now()
        
        return cls(
            key=data['key'],
            type=data['type'],
            entropy=data['entropy'],
            context=data['context'],
            source=data['source'],
            start=data['start'],
            end=data['end'],
            context_score=data.get('context_score', 0.0),
            scan_method=data.get('scan_method', 'unknown'),
            source_type=data.get('source_type', 'unknown'),
            timestamp=timestamp,
            validation=data.get('validation'),
            metadata=data.get('metadata', {})
        )
    
    def is_valid(self) -> Optional[bool]:
        """检查是否已验证为有效"""
        if self.validation:
            return self.validation.get('valid')
        return None
    
    def get_masked_key(self, show_chars: int = 8) -> str:
        """获取掩码后的Key"""
        if len(self.key) <= show_chars * 2:
            return self.key
        
        return f"{self.key[:show_chars]}...{self.key[-show_chars:]}"
    
    def get_risk_level(self) -> str:
        """获取风险级别"""
        if self.validation and self.validation.get('valid') is True:
            return "HIGH"
        elif self.entropy >= 4.5 and self.context_score >= 0.5:
            return "MEDIUM"
        elif self.entropy >= 4.0:
            return "LOW"
        else:
            return "VERY_LOW"


@dataclass
class ScanResult:
    """扫描结果"""
    matches: List[APIKeyMatch] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    scan_config: Dict[str, Any] = field(default_factory=dict)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    def add_match(self, match: APIKeyMatch):
        """添加匹配结果"""
        self.matches.append(match)
    
    def finish_scan(self):
        """完成扫描"""
        self.end_time = datetime.now()
        self._calculate_statistics()
    
    def _calculate_statistics(self):
        """计算统计信息"""
        if not self.matches:
            self.statistics = {
                'total_matches': 0,
                'unique_keys': 0,
                'key_types': {},
                'source_types': {},
                'risk_levels': {},
                'validation_stats': {},
                'duration_seconds': 0
            }
            return
        
        # 基础统计
        total_matches = len(self.matches)
        unique_keys = len(set(match.key for match in self.matches))
        
        # 按类型统计
        key_types = {}
        source_types = {}
        risk_levels = {}
        validation_stats = {'validated': 0, 'valid': 0, 'invalid': 0}
        
        for match in self.matches:
            # Key类型统计
            key_types[match.type] = key_types.get(match.type, 0) + 1
            
            # 来源类型统计
            source_types[match.source_type] = source_types.get(match.source_type, 0) + 1
            
            # 风险级别统计
            risk_level = match.get_risk_level()
            risk_levels[risk_level] = risk_levels.get(risk_level, 0) + 1
            
            # 验证统计
            if match.validation:
                validation_stats['validated'] += 1
                if match.validation.get('valid') is True:
                    validation_stats['valid'] += 1
                elif match.validation.get('valid') is False:
                    validation_stats['invalid'] += 1
        
        # 计算持续时间
        duration = 0
        if self.end_time and self.start_time:
            duration = (self.end_time - self.start_time).total_seconds()
        
        self.statistics = {
            'total_matches': total_matches,
            'unique_keys': unique_keys,
            'key_types': key_types,
            'source_types': source_types,
            'risk_levels': risk_levels,
            'validation_stats': validation_stats,
            'duration_seconds': duration
        }
    
    def get_matches_by_type(self, key_type: str) -> List[APIKeyMatch]:
        """按类型获取匹配结果"""
        return [match for match in self.matches if match.type == key_type]
    
    def get_matches_by_source(self, source: str) -> List[APIKeyMatch]:
        """按来源获取匹配结果"""
        return [match for match in self.matches if match.source == source]
    
    def get_high_risk_matches(self) -> List[APIKeyMatch]:
        """获取高风险匹配结果"""
        return [match for match in self.matches if match.get_risk_level() == "HIGH"]
    
    def get_validated_matches(self) -> List[APIKeyMatch]:
        """获取已验证的匹配结果"""
        return [match for match in self.matches if match.validation is not None]
    
    def deduplicate(self) -> 'ScanResult':
        """去重"""
        seen_keys = set()
        unique_matches = []
        
        for match in self.matches:
            if match.key not in seen_keys:
                seen_keys.add(match.key)
                unique_matches.append(match)
        
        result = ScanResult(
            matches=unique_matches,
            scan_config=self.scan_config,
            start_time=self.start_time,
            end_time=self.end_time
        )
        result._calculate_statistics()
        return result
    
    def filter_by_entropy(self, min_entropy: float) -> 'ScanResult':
        """按熵值过滤"""
        filtered_matches = [match for match in self.matches if match.entropy >= min_entropy]
        
        result = ScanResult(
            matches=filtered_matches,
            scan_config=self.scan_config,
            start_time=self.start_time,
            end_time=self.end_time
        )
        result._calculate_statistics()
        return result
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'matches': [match.to_dict() for match in self.matches],
            'statistics': self.statistics,
            'scan_config': self.scan_config,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ScanResult':
        """从字典创建"""
        matches = [APIKeyMatch.from_dict(match_data) for match_data in data.get('matches', [])]
        
        start_time = data.get('start_time')
        if isinstance(start_time, str):
            start_time = datetime.fromisoformat(start_time)
        
        end_time = data.get('end_time')
        if isinstance(end_time, str):
            end_time = datetime.fromisoformat(end_time)
        
        return cls(
            matches=matches,
            statistics=data.get('statistics', {}),
            scan_config=data.get('scan_config', {}),
            start_time=start_time or datetime.now(),
            end_time=end_time
        )
    
    def export_json(self, file_path: str = None) -> str:
        """导出为JSON"""
        json_data = json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
        
        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_data)
        
        return json_data
    
    def export_csv(self, file_path: str = None) -> str:
        """导出为CSV"""
        import csv
        import io
        
        output = io.StringIO()
        
        if self.matches:
            # 准备CSV数据
            csv_data = []
            for match in self.matches:
                row = {
                    'key': match.get_masked_key(),
                    'type': match.type,
                    'entropy': match.entropy,
                    'source': match.source,
                    'source_type': match.source_type,
                    'risk_level': match.get_risk_level(),
                    'context_score': match.context_score,
                    'timestamp': match.timestamp.isoformat(),
                    'validated': match.is_valid()
                }
                csv_data.append(row)
            
            # 写入CSV
            fieldnames = csv_data[0].keys()
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
        
        csv_content = output.getvalue()
        
        if file_path:
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                f.write(csv_content)
        
        return csv_content
