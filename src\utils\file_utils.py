"""
文件操作工具
"""
import os
import json
import csv
import yaml
from pathlib import Path
from typing import List, Dict, Optional, Union, Generator
import logging
import mimetypes
import hashlib

logger = logging.getLogger(__name__)


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def read_file(file_path: str, encoding: str = 'utf-8') -> Optional[str]:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None
    
    @staticmethod
    def write_file(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
        """写入文件内容"""
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            return False
    
    @staticmethod
    def read_json(file_path: str) -> Optional[Dict]:
        """读取JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            return None
    
    @staticmethod
    def write_json(file_path: str, data: Dict, indent: int = 2) -> bool:
        """写入JSON文件"""
        try:
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Error writing JSON file {file_path}: {e}")
            return False
    
    @staticmethod
    def read_csv(file_path: str) -> Optional[List[Dict]]:
        """读取CSV文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                return list(reader)
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return None
    
    @staticmethod
    def write_csv(file_path: str, data: List[Dict], fieldnames: List[str] = None) -> bool:
        """写入CSV文件"""
        try:
            if not data:
                return True
            
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            if not fieldnames:
                fieldnames = list(data[0].keys())
            
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            return True
        except Exception as e:
            logger.error(f"Error writing CSV file {file_path}: {e}")
            return False
    
    @staticmethod
    def read_yaml(file_path: str) -> Optional[Dict]:
        """读取YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error reading YAML file {file_path}: {e}")
            return None
    
    @staticmethod
    def write_yaml(file_path: str, data: Dict) -> bool:
        """写入YAML文件"""
        try:
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            return True
        except Exception as e:
            logger.error(f"Error writing YAML file {file_path}: {e}")
            return False
    
    @staticmethod
    def find_files(directory: str, patterns: List[str] = None, 
                  exclude_patterns: List[str] = None) -> List[str]:
        """查找文件"""
        if patterns is None:
            patterns = ['*']
        
        if exclude_patterns is None:
            exclude_patterns = []
        
        found_files = []
        directory_path = Path(directory)
        
        for pattern in patterns:
            for file_path in directory_path.rglob(pattern):
                if file_path.is_file():
                    # 检查排除模式
                    should_exclude = False
                    for exclude_pattern in exclude_patterns:
                        if file_path.match(exclude_pattern):
                            should_exclude = True
                            break
                    
                    if not should_exclude:
                        found_files.append(str(file_path))
        
        return found_files
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict:
        """获取文件信息"""
        try:
            path = Path(file_path)
            stat = path.stat()
            
            return {
                'path': str(path),
                'name': path.name,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'created': stat.st_ctime,
                'extension': path.suffix,
                'mime_type': mimetypes.guess_type(str(path))[0],
                'is_text': FileUtils.is_text_file(file_path)
            }
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return {}
    
    @staticmethod
    def is_text_file(file_path: str) -> bool:
        """判断是否为文本文件"""
        try:
            mime_type, _ = mimetypes.guess_type(file_path)
            
            if mime_type:
                return mime_type.startswith('text/') or mime_type in [
                    'application/json',
                    'application/xml',
                    'application/javascript',
                    'application/x-yaml'
                ]
            
            # 通过扩展名判断
            text_extensions = {
                '.txt', '.py', '.js', '.html', '.css', '.json', '.xml',
                '.yaml', '.yml', '.md', '.rst', '.ini', '.cfg', '.conf',
                '.env', '.log', '.sql', '.sh', '.bat', '.ps1', '.php',
                '.rb', '.go', '.java', '.c', '.cpp', '.h', '.hpp'
            }
            
            return Path(file_path).suffix.lower() in text_extensions
            
        except Exception:
            return False
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """计算文件哈希值"""
        try:
            hash_func = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            
            return hash_func.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {e}")
            return None
    
    @staticmethod
    def create_backup(file_path: str, backup_dir: str = None) -> Optional[str]:
        """创建文件备份"""
        try:
            path = Path(file_path)
            
            if backup_dir:
                backup_path = Path(backup_dir) / f"{path.stem}.backup{path.suffix}"
            else:
                backup_path = path.with_suffix(f".backup{path.suffix}")
            
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            import shutil
            shutil.copy2(file_path, backup_path)
            
            return str(backup_path)
        except Exception as e:
            logger.error(f"Error creating backup for {file_path}: {e}")
            return None
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """清理文件名，移除非法字符"""
        import re
        
        # 移除或替换非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(illegal_chars, '_', filename)
        
        # 移除多余的空格和点
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        cleaned = re.sub(r'\.+$', '', cleaned)
        
        return cleaned
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """获取目录大小"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        continue
            return total_size
        except Exception as e:
            logger.error(f"Error calculating directory size for {directory}: {e}")
            return 0
    
    @staticmethod
    def split_file(file_path: str, chunk_size: int = 1024*1024) -> Generator[str, None, None]:
        """分块读取大文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    yield chunk
        except Exception as e:
            logger.error(f"Error reading file chunks from {file_path}: {e}")


# 全局文件工具实例
file_utils = FileUtils()
