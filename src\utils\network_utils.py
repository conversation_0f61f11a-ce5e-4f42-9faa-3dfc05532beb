"""
网络工具
"""
import asyncio
import aiohttp
import socket
import ipaddress
from urllib.parse import urlparse, urljoin, quote
from typing import Dict, List, Optional, Tuple
import logging
import time
import ssl

logger = logging.getLogger(__name__)


class NetworkUtils:
    """网络工具类"""
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """验证URL是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def normalize_url(url: str) -> str:
        """标准化URL"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        parsed = urlparse(url)
        
        # 移除默认端口
        netloc = parsed.netloc
        if ':80' in netloc and parsed.scheme == 'http':
            netloc = netloc.replace(':80', '')
        elif ':443' in netloc and parsed.scheme == 'https':
            netloc = netloc.replace(':443', '')
        
        # 重构URL
        normalized = f"{parsed.scheme}://{netloc}{parsed.path}"
        
        if parsed.query:
            normalized += f"?{parsed.query}"
        
        return normalized
    
    @staticmethod
    def extract_domain(url: str) -> Optional[str]:
        """提取域名"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except Exception:
            return None
    
    @staticmethod
    def is_internal_ip(ip: str) -> bool:
        """判断是否为内网IP"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            return ip_obj.is_private or ip_obj.is_loopback or ip_obj.is_link_local
        except Exception:
            return False
    
    @staticmethod
    async def resolve_domain(domain: str) -> Optional[str]:
        """解析域名到IP"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.getaddrinfo(domain, None)
            if result:
                return result[0][4][0]  # 返回第一个IP
        except Exception as e:
            logger.debug(f"Failed to resolve domain {domain}: {e}")
        return None
    
    @staticmethod
    async def check_port_open(host: str, port: int, timeout: float = 5.0) -> bool:
        """检查端口是否开放"""
        try:
            future = asyncio.open_connection(host, port)
            reader, writer = await asyncio.wait_for(future, timeout=timeout)
            writer.close()
            await writer.wait_closed()
            return True
        except Exception:
            return False
    
    @staticmethod
    async def get_response_headers(url: str, timeout: float = 10.0) -> Optional[Dict]:
        """获取响应头"""
        try:
            connector = aiohttp.TCPConnector(ssl=False)
            timeout_obj = aiohttp.ClientTimeout(total=timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_obj
            ) as session:
                async with session.head(url) as response:
                    return dict(response.headers)
        except Exception as e:
            logger.debug(f"Failed to get headers for {url}: {e}")
            return None
    
    @staticmethod
    async def check_url_accessibility(url: str, timeout: float = 10.0) -> Dict:
        """检查URL可访问性"""
        start_time = time.time()
        
        result = {
            'url': url,
            'accessible': False,
            'status_code': None,
            'response_time': None,
            'error': None,
            'redirect_url': None,
            'content_type': None,
            'content_length': None
        }
        
        try:
            connector = aiohttp.TCPConnector(ssl=False)
            timeout_obj = aiohttp.ClientTimeout(total=timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_obj
            ) as session:
                async with session.get(url, allow_redirects=True) as response:
                    result['accessible'] = True
                    result['status_code'] = response.status
                    result['response_time'] = time.time() - start_time
                    result['redirect_url'] = str(response.url) if str(response.url) != url else None
                    result['content_type'] = response.headers.get('content-type')
                    result['content_length'] = response.headers.get('content-length')
                    
        except Exception as e:
            result['error'] = str(e)
            result['response_time'] = time.time() - start_time
        
        return result
    
    @staticmethod
    def extract_urls_from_text(text: str) -> List[str]:
        """从文本中提取URL"""
        import re
        
        url_pattern = re.compile(
            r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        
        urls = url_pattern.findall(text)
        return list(set(urls))  # 去重
    
    @staticmethod
    def build_url(base_url: str, path: str = '', params: Dict = None) -> str:
        """构建URL"""
        url = urljoin(base_url, path)
        
        if params:
            query_string = '&'.join([f"{quote(str(k))}={quote(str(v))}" for k, v in params.items()])
            url += f"?{query_string}"
        
        return url
    
    @staticmethod
    def parse_user_agent(user_agent: str) -> Dict:
        """解析User-Agent"""
        import re
        
        result = {
            'browser': 'Unknown',
            'version': 'Unknown',
            'os': 'Unknown',
            'device': 'Desktop'
        }
        
        # 浏览器检测
        browsers = {
            'Chrome': r'Chrome/([0-9.]+)',
            'Firefox': r'Firefox/([0-9.]+)',
            'Safari': r'Safari/([0-9.]+)',
            'Edge': r'Edge/([0-9.]+)',
            'Opera': r'Opera/([0-9.]+)'
        }
        
        for browser, pattern in browsers.items():
            match = re.search(pattern, user_agent)
            if match:
                result['browser'] = browser
                result['version'] = match.group(1)
                break
        
        # 操作系统检测
        if 'Windows' in user_agent:
            result['os'] = 'Windows'
        elif 'Mac OS' in user_agent:
            result['os'] = 'macOS'
        elif 'Linux' in user_agent:
            result['os'] = 'Linux'
        elif 'Android' in user_agent:
            result['os'] = 'Android'
            result['device'] = 'Mobile'
        elif 'iOS' in user_agent:
            result['os'] = 'iOS'
            result['device'] = 'Mobile'
        
        return result
    
    @staticmethod
    async def get_ssl_info(hostname: str, port: int = 443) -> Optional[Dict]:
        """获取SSL证书信息"""
        try:
            context = ssl.create_default_context()
            
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    return {
                        'subject': dict(x[0] for x in cert['subject']),
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'version': cert['version'],
                        'serial_number': cert['serialNumber'],
                        'not_before': cert['notBefore'],
                        'not_after': cert['notAfter'],
                        'san': cert.get('subjectAltName', [])
                    }
        except Exception as e:
            logger.debug(f"Failed to get SSL info for {hostname}: {e}")
            return None
    
    @staticmethod
    def calculate_network_delay(host: str, port: int = 80, count: int = 3) -> Optional[float]:
        """计算网络延迟"""
        delays = []
        
        for _ in range(count):
            try:
                start_time = time.time()
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    delay = (time.time() - start_time) * 1000  # 转换为毫秒
                    delays.append(delay)
                else:
                    return None
                    
            except Exception:
                return None
        
        return sum(delays) / len(delays) if delays else None
    
    @staticmethod
    def get_public_ip() -> Optional[str]:
        """获取公网IP"""
        try:
            import requests
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin')
        except Exception:
            pass
        
        try:
            import requests
            response = requests.get('https://api.ipify.org', timeout=10)
            if response.status_code == 200:
                return response.text.strip()
        except Exception:
            pass
        
        return None
    
    @staticmethod
    def is_cloudflare_protected(headers: Dict) -> bool:
        """检查是否受Cloudflare保护"""
        cf_headers = [
            'cf-ray', 'cf-cache-status', 'cf-request-id',
            'server'
        ]
        
        for header in cf_headers:
            value = headers.get(header, '').lower()
            if 'cloudflare' in value or header.startswith('cf-'):
                return True
        
        return False


# 全局网络工具实例
network_utils = NetworkUtils()
