# OpenRouter API配置文件

import os
from openrouter import OpenRouter

# OpenRouter API Key
OPENROUTER_API_KEY = "sk-or-v1-1234567890abcdef1234567890abcdef12345678"

# 配置客户端
client = OpenRouter(
    api_key=OPENROUTER_API_KEY,
    base_url="https://openrouter.ai/api/v1"
)

# 其他配置
MODEL_NAME = "anthropic/claude-3-haiku"
MAX_TOKENS = 1000

def get_completion(prompt):
    """获取AI回复"""
    response = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[{"role": "user", "content": prompt}],
        max_tokens=MAX_TOKENS
    )
    return response.choices[0].message.content

# 测试用的无效key
INVALID_KEY = "sk-or-v1-invalid1234567890abcdef1234567890abcdef"

# 环境变量示例
os.environ["OPENROUTER_API_KEY"] = "sk-or-v1-abcdef1234567890abcdef1234567890abcdef123456"
