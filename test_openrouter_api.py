#!/usr/bin/env python3
"""
测试OpenRouter API Key的实际功能
"""
import requests
import json

# OpenRouter API Key
API_KEY = "sk-or-v1-test1234567890abcdef1234567890abcdef123456"

def test_openrouter_chat(question="你好，请介绍一下你自己"):
    """测试OpenRouter聊天功能"""
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 使用一个轻量级的模型进行测试
    data = {
        "model": "openai/gpt-3.5-turbo",
        "messages": [
            {
                "role": "user", 
                "content": question
            }
        ],
        "max_tokens": 150,
        "temperature": 0.7
    }
    
    try:
        print(f"🤖 正在向OpenRouter发送问题: {question}")
        print("=" * 50)
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                answer = result['choices'][0]['message']['content']
                print(f"✅ API调用成功!")
                print(f"📝 AI回答: {answer}")
                
                # 显示使用统计
                if 'usage' in result:
                    usage = result['usage']
                    print(f"\n📊 Token使用统计:")
                    print(f"   输入Token: {usage.get('prompt_tokens', 'N/A')}")
                    print(f"   输出Token: {usage.get('completion_tokens', 'N/A')}")
                    print(f"   总Token: {usage.get('total_tokens', 'N/A')}")
                
                return True, answer
            else:
                print("❌ 响应格式异常")
                print(f"响应内容: {result}")
                return False, None
                
        else:
            print(f"❌ API调用失败")
            print(f"错误信息: {response.text}")
            return False, None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False, None
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
        return False, None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False, None

def test_multiple_questions():
    """测试多个问题"""
    questions = [
        "什么是人工智能？",
        "请用一句话解释机器学习",
        "OpenRouter是什么？",
        "写一个Python的Hello World程序"
    ]
    
    print("🚀 开始多问题测试...")
    print("=" * 60)
    
    success_count = 0
    
    for i, question in enumerate(questions, 1):
        print(f"\n📋 测试 {i}/{len(questions)}")
        success, answer = test_openrouter_chat(question)
        
        if success:
            success_count += 1
        
        print("-" * 40)
    
    print(f"\n📈 测试结果: {success_count}/{len(questions)} 成功")
    print(f"成功率: {success_count/len(questions)*100:.1f}%")

if __name__ == "__main__":
    print("🔑 OpenRouter API Key测试程序")
    print(f"使用的API Key: {API_KEY[:20]}...")
    print("=" * 60)
    
    # 单个问题测试
    print("\n1️⃣ 单个问题测试:")
    test_openrouter_chat("你好，请简单介绍一下OpenRouter平台")
    
    print("\n" + "=" * 60)
    
    # 多个问题测试
    print("\n2️⃣ 多个问题测试:")
    test_multiple_questions()
    
    print("\n✨ 测试完成!")
