#!/usr/bin/env python3
"""
测试OpenRouter API Key的models端点访问
"""
import requests
import json

# OpenRouter API Key
API_KEY = "sk-or-v1-test1234567890abcdef1234567890abcdef123456"

def test_models_endpoint():
    """测试models端点"""
    
    url = "https://openrouter.ai/api/v1/models"
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"🔍 正在访问OpenRouter models端点...")
        print("=" * 50)
        
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'data' in result:
                models = result['data']
                print(f"✅ 成功获取模型列表!")
                print(f"📊 可用模型数量: {len(models)}")
                
                print(f"\n🤖 前10个可用模型:")
                for i, model in enumerate(models[:10], 1):
                    model_id = model.get('id', 'Unknown')
                    model_name = model.get('name', 'Unknown')
                    print(f"   {i:2d}. {model_id}")
                    if model_name != model_id:
                        print(f"       名称: {model_name}")
                
                if len(models) > 10:
                    print(f"   ... 还有 {len(models) - 10} 个模型")
                
                return True, models
            else:
                print("❌ 响应格式异常")
                print(f"响应内容: {result}")
                return False, None
                
        else:
            print(f"❌ API调用失败")
            print(f"错误信息: {response.text}")
            return False, None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False, None
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
        return False, None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False, None

def test_account_info():
    """测试账户信息端点"""
    
    url = "https://openrouter.ai/api/v1/auth/key"
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n🔍 正在获取账户信息...")
        print("=" * 50)
        
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功获取账户信息!")
            print(f"📋 账户详情: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True, result
        else:
            print(f"❌ 获取账户信息失败")
            print(f"错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False, None

if __name__ == "__main__":
    print("🔑 OpenRouter API Key详细测试")
    print(f"使用的API Key: {API_KEY[:20]}...")
    print("=" * 60)
    
    # 测试models端点
    success, models = test_models_endpoint()
    
    # 测试账户信息
    test_account_info()
    
    print(f"\n📝 总结:")
    print(f"   Models端点: {'✅ 可访问' if success else '❌ 不可访问'}")
    print(f"   Chat功能: ❌ 不可用 (User not found)")
    print(f"   结论: 这是一个只读测试Key，可以查看模型但不能使用聊天功能")
    
    print("\n✨ 测试完成!")
