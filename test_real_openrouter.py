# 真实OpenRouter API Key测试

import openrouter

# 配置OpenRouter客户端
OPENROUTER_API_KEY = "sk-or-v1-test1234567890abcdef1234567890abcdef123456"

client = openrouter.Client(
    api_key=OPENROUTER_API_KEY,
    base_url="https://openrouter.ai/api/v1"
)

# 测试配置
def test_openrouter():
    """测试OpenRouter连接"""
    try:
        response = client.chat.completions.create(
            model="openai/gpt-oss-120b",
            messages=[{"role": "user", "content": "Hello!"}],
            max_tokens=50
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    result = test_openrouter()
    print(f"OpenRouter response: {result}")
