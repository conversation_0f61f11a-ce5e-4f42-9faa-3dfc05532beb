# 测试API Key样本文件

# Gemini API Key
GOOGLE_API_KEY=AIzaSyDaGmWKa4JsXZ-HjGw7ISLan_Tos80ksOE

# OpenRouter API Key  
OPENROUTER_API_KEY=sk-or-v1-1234567890abcdef1234567890abcdef12345678

# OpenAI API Key
OPENAI_API_KEY=sk-1234567890abcdef1234567890abcdef12345678901234

# GitHub Token
GITHUB_TOKEN=ghp_1234567890abcdef1234567890abcdef123456

# 一些普通文本
这是一些普通的文本内容，不包含API Key。

# 低熵值字符串（应该被过滤）
password=123456
simple_key=abcdefg

# 高熵值但不匹配模式的字符串
random_string=xK9mP2vQ8nR5tL7wE3yU6iO1sA4dF0gH
